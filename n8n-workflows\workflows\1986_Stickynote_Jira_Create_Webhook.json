{"id": "uD31xU0VYjogxWoY", "meta": {"instanceId": "03e9d14e9196363fe7191ce21dc0bb17387a6e755dcc9acc4f5904752919dca8"}, "name": "Create_Unique_Jira_tickets_from_Splunk_alerts", "tags": [{"id": "GCHVocImoXoEVnzP", "name": "🛠️ In progress", "createdAt": "2023-10-31T02:17:21.618Z", "updatedAt": "2023-10-31T02:17:21.618Z"}, {"id": "QPJKatvLSxxtrE8U", "name": "Secops", "createdAt": "2023-10-31T02:15:11.396Z", "updatedAt": "2023-10-31T02:15:11.396Z"}], "nodes": [{"id": "3f9fa220-1966-4478-b7db-c39056564c9d", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-640, 320], "webhookId": "f2a52578-2fef-40a6-a7ff-e03f6b751a02", "parameters": {"path": "f2a52578-2fef-40a6-a7ff-e03f6b751a02", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"id": "375ac47e-7975-45cb-b7c1-cef1c7fca701", "name": "Add Ticket Comment", "type": "n8n-nodes-base.jira", "position": [240, 520], "parameters": {"comment": "=Timestamp: {{ $('Set Host Name').item.json.body.timestamp }}\nDescription: {{ $('Set Host Name').item.json.body.description }}", "options": {}, "issueKey": "={{ $json.key }}", "resource": "issueComment"}, "credentials": {"jiraSoftwareCloudApi": {"id": "OYvpDV2Q42eY6iyA", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "a5dea875-6adf-4d18-aeb9-5fe31a0ebfae", "name": "Search Ticket", "type": "n8n-nodes-base.jira", "position": [-200, 320], "parameters": {"options": {"jql": "=splunkhostname ~ \"{{ $json['splunk-host-name'] }}\" "}, "operation": "getAll"}, "credentials": {"jiraSoftwareCloudApi": {"id": "OYvpDV2Q42eY6iyA", "name": "<PERSON>"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "3dac410e-1e37-463d-9aba-bc6abf3889f7", "name": "Set Host Name", "type": "n8n-nodes-base.set", "position": [-420, 320], "parameters": {"values": {"string": [{"name": "splunk-host-name", "value": "={{ $json.body.inputs.A.key['host.name'].replace(/[^a-zA-Z0-9 ]/g, '') }}"}]}, "options": {}}, "typeVersion": 2}, {"id": "465ec3b0-dd16-482e-b4b6-f8ed91fbb11b", "name": "IF Ticket Not Exists", "type": "n8n-nodes-base.if", "position": [20, 320], "parameters": {"conditions": {"string": [{"value1": "={{ $json.key }}", "operation": "isEmpty"}]}}, "typeVersion": 1}, {"id": "1315b76b-39fc-4fd3-9a45-a91e5e873874", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1120, -26.960531840248223], "parameters": {"width": 643.8620281403546, "height": 537.************, "content": "![VirusTotal](https://i.imgur.com/spMd6AQ.png)\n## Webhook Node \nTo setup your webhook integration for Splunk, first ensure that splunk is setup to send alerts to a webhook by visiting the [Setup Guide here](https://docs.splunk.com/observability/en/admin/notif-services/webhook.html). You will copy the n8n webhook url opening the webhook node below. \n- **Form Access URLs**:\n  - **Execute Mode**: `https://n8n.domain.com/webhook/test/webhookpath` - Use this to execute the workflow interactively within the n8n canvas. Hit the 'Execute Workflow' button to see real-time execution results. We have pinned data in the webhook node to make testing easier. \n  - **Silent Mode**: `https://n8n.domain.com/webhook/webhookpath` - Use this for background execution without canvas updates. Results will be logged silently and can be reviewed in the 'Executions' tab."}, "typeVersion": 1}, {"id": "636425b9-a11f-4891-aa00-2f3c42956c01", "name": "Create Ticket", "type": "n8n-nodes-base.jira", "position": [240, 160], "parameters": {"project": {"__rl": true, "mode": "list", "value": "10001", "cachedResultName": "Service Desk"}, "summary": "=Splunk <PERSON><PERSON> for host {{ $('Set Host Name').item.json.body.inputs.A.key[\"host.name\"] }}:  {{ $('Set Host Name').item.json.body.description }}", "issueType": {"__rl": true, "mode": "list", "value": "10004", "cachedResultName": "[System] Incident"}, "additionalFields": {"description": "={{ $('Set Host Name').item.json.body.description }}\n\n{{ $('Set Host Name').item.json.body.messageBody }}", "customFieldsUi": {"customFieldsValues": [{"fieldId": {"__rl": true, "mode": "id", "value": "customfield_10063"}, "fieldValue": "={{ $('Webhook').item.json[\"body\"][\"inputs\"][\"A\"][\"key\"][\"host.name\"].replace(/[^a-zA-Z0-9 ]/g, '') }}"}]}}}, "credentials": {"jiraSoftwareCloudApi": {"id": "OYvpDV2Q42eY6iyA", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "47af8bdb-e0da-4923-8f0a-05deb86ac1b3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-460, 98.72468966845895], "parameters": {"width": 401.99970102055784, "height": 413.43480804607805, "content": "![VirusTotal](https://upload.wikimedia.org/wikipedia/commons/thumb/8/82/Jira_%28Software%29_logo.svg/320px-Jira_%28Software%29_logo.svg.png)\n## Normalize Hostname \nTo ensure no special characters are passed into jira and create issues, this set node removes special characters from the `splunk-host-name` and uses that to search and create tickets. This host name is saved as a custom field. "}, "typeVersion": 1}, {"id": "c0bf09e6-ca08-4db6-aff0-a6528a8fb03b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [180, -21.934709587377256], "parameters": {"width": 401.99970102055784, "height": 348.38243930996134, "content": "![VirusTotal](https://upload.wikimedia.org/wikipedia/commons/thumb/8/82/Jira_%28Software%29_logo.svg/320px-Jira_%28Software%29_logo.svg.png)\n## Create a new ticket\nThis creates a new ticket in your Prjoect and issue type. Ensure to update these values to ensure it works correctly. "}, "typeVersion": 1}, {"id": "a175e343-83ed-4442-94df-7e7027b8c687", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [180, 340], "parameters": {"width": 401.99970102055784, "height": 341.08777742613927, "content": "![VirusTotal](https://upload.wikimedia.org/wikipedia/commons/thumb/8/82/Jira_%28Software%29_logo.svg/320px-Jira_%28Software%29_logo.svg.png)\n## Add Ticket Comment\nThis adds the alert as a comment in the existing ticket, to ensure the data is not duplicated. "}, "typeVersion": 1}, {"id": "09143b8c-a4ce-4791-8937-3333d24b6e01", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-40, 100.50445897107033], "parameters": {"width": 193.6032856277124, "height": 415.27445353029793, "content": "## Check if ticket found\nThis checks `$json.key` to see if the value was found, and route accordingly."}, "typeVersion": 1}], "active": false, "pinData": {"Webhook": [{"json": {"body": {"tip": null, "rule": "n8n-test", "inputs": {"A": {"key": {"os.type": "linux", "host.name": "n8n-enterprise-demo", "sf_metric": "cpu.utilization"}, "value": "0.1670342357065173", "fragment": "data('cpu.utilization').publish(label='A')"}, "_S2": {"value": "0.2", "fragment": "threshold(0.2)"}}, "status": "ok", "detector": "n8n-test", "imageUrl": "https://static.eu0.signalfx.com/signed/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjb20uc2lnbmFsZnguYXBwIiwiZXhwIjoxNjk0NjE0NjI2LCJpSWQiOiJGNVZBcTEwQUVBQSIsIm9JZCI6IkY1V0JKZ2lBSUFBIiwiYlQiOiJlbmQifQ.udzyF5-HqKyV_EMRmT51EtgECK9g-wanl8nx_MH0i9Q/async", "severity": "Critical", "eventType": "F5Vx1EuAAKc__F5V-TcTAEJ8__n8n-test", "sf_schema": 2, "timestamp": "2023-09-06T14:17:00Z", "detectorId": "F5V-TcTAEJ8", "incidentId": "F5VAq10AEAA", "runbookUrl": null, "description": "The value of cpu.utilization is above 0.2.", "detectorUrl": "https://app.eu0.signalfx.com/#/detector/F5V-TcTAEJ8/edit?incidentId=F5VAq10AEAA&is=ok", "messageBody": "Rule \"n8n-test\" in detector \"n8n-test\" cleared at Wed, 6 Sep 2023 14:17:00 GMT.\n\nCurrent signal value for n8n.test: 0.1670342357065173\n\nSignal details:\n{sf_metric=cpu.utilization, host.name=n8n-enterprise-demo, os.type=linux}", "messageTitle": "Back to normal: n8n-test (n8n-test)", "statusExtended": "ok", "detectOnCondition": "when(A > threshold(0.2))", "originatingMetric": "cpu.utilization", "triggeredWhileMuted": false}, "query": {}, "params": {}, "headers": {"host": "internal.users.n8n.cloud", "x-real-ip": "**********", "user-agent": "Apache-HttpClient/4.5.14 (Java/1.8.0_372)", "content-type": "application/json; charset=utf-8", "content-length": "1366", "accept-encoding": "gzip,deflate", "x-forwarded-for": "**********", "x-forwarded-host": "internal.users.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "e591fa1c2d01"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "3985cac2-7f23-4d27-b826-0edfb0544b58", "connections": {"Webhook": {"main": [[{"node": "Set Host Name", "type": "main", "index": 0}]]}, "Search Ticket": {"main": [[{"node": "IF Ticket Not Exists", "type": "main", "index": 0}]]}, "Set Host Name": {"main": [[{"node": "Search Ticket", "type": "main", "index": 0}]]}, "IF Ticket Not Exists": {"main": [[{"node": "Create Ticket", "type": "main", "index": 0}], [{"node": "Add Ticket Comment", "type": "main", "index": 0}]]}}}