<?php
// Set proper content type for JSON responses
header('Content-Type: application/json');

// Define upload directory with proper path handling
$uploadDir = __DIR__ . '/email_files/';

// Ensure the directory exists and is secure
if (!is_dir($uploadDir)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Upload directory does not exist.']);
    exit;
}

// Check if the request method is POST (more secure for deletion operations)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the filename or full URL from the request
    $fileParam = isset($_POST['filename']) ? $_POST['filename'] : null;
    $fullUrl = isset($_POST['url']) ? $_POST['url'] : null;
    
    // If URL is provided, extract the filename from it
    if ($fullUrl && !$fileParam) {
        $parsedUrl = parse_url($fullUrl);
        if (isset($parsedUrl['path'])) {
            $fileParam = basename($parsedUrl['path']);
            // URL decode in case of encoded characters
            $fileParam = urldecode($fileParam);
        }
    }

    if ($fileParam) {
        // Sanitize the filename: remove directory traversal characters
        // And keep only characters allowed in the original upload script (Arabic, letters, numbers, dots, dashes, underscores)
        $safeName = basename($fileParam); // Basic sanitization
        $safeName = preg_replace('/[^\p{Arabic}a-zA-Z0-9\.\-\_]/u', '_', $safeName);

        if (empty($safeName)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid filename provided after sanitization.']);
            exit;
        }

        $filePath = $uploadDir . $safeName;

        // Additional security check - ensure the resolved path is within the upload directory
        $realFilePath = realpath($filePath);
        $realUploadDir = realpath($uploadDir);
        
        if ($realFilePath === false || strpos($realFilePath, $realUploadDir) !== 0) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Access denied.']);
            exit;
        }

        if (file_exists($filePath)) {
            if (!is_writable($filePath)) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'File is not writable. Check file permissions.',
                    'filename' => $safeName
                ]);
            } elseif (!is_writable($uploadDir)) { // Check if the directory itself is writable
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Upload directory is not writable. Check directory permissions.',
                    'filename' => $safeName
                ]);
            } elseif (unlink($filePath)) {
                echo json_encode(['success' => true, 'message' => 'File deleted successfully.', 'filename' => $safeName]);
            } else {
                $error = error_get_last();
                $errorMessage = 'Failed to delete file.';
                if ($error !== null) {
                    $errorMessage .= ' System error: ' . $error['message'];
                }
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => $errorMessage, 'filename' => $safeName]);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'File not found.', 'filename' => $safeName]);
        }
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Filename or URL not provided.']);
    }
} else {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Invalid request method. Please use POST.']);
}
?>
