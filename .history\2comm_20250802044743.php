<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

if (!empty($_FILES)) {
    echo "<pre>_FILES:\n";
    print_r($_FILES);
    echo "</pre>";
}

if (!empty($_POST)) {
    echo "<pre>_POST:\n";
    print_r($_POST);
    echo "</pre>";
}
ob_start(); 
ini_set('memory_limit', '512M');

// Prevent caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");


error_reporting(E_ALL);
ini_set('display_errors', 1);

// Display PHP upload settings for debugging
if (isset($_GET['debug'])) {
    echo "<pre>";
    echo "PHP Upload Settings:\n";
    echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
    echo "post_max_size: " . ini_get('post_max_size') . "\n";
    echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
    echo "file_uploads: " . (ini_get('file_uploads') ? 'On' : 'Off') . "\n";
    echo "upload_tmp_dir: " . ini_get('upload_tmp_dir') . "\n";
    echo "Current directory: " . __DIR__ . "\n";
    echo "Upload directory: " . __DIR__ . '/1comm/' . "\n";
    echo "Upload dir exists: " . (file_exists(__DIR__ . '/1comm/') ? 'Yes' : 'No') . "\n";
    echo "Upload dir writable: " . (is_writable(__DIR__ . '/1comm/') ? 'Yes' : 'No') . "\n";
    echo "</pre>";
    exit;
}

session_start(); // Start the session

// Set the database name
$current_db_name = 'icredept_1comm'; // Default database

// Function to make URLs in text clickable
function makeLinksClickable($text) {
    // Regular expression to match URLs
    $pattern = '/(https?:\/\/[\w\-\.\/?\&%=\+\~\#\:;\,\!\@]+)/i';
    
    // Replace URLs with anchor tags
    $replacement = '<a href="$1" target="_blank" class="text-blue-600 hover:underline break-all">$1</a>';
    
    // Perform the replacement
    return preg_replace($pattern, $replacement, $text);
}

// Prevent browser caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Database connection
$SETTINGS["hostname"] = '*************';
$SETTINGS["mysql_user"] = 'tariq';
$SETTINGS["mysql_pass"] = 'Hgd!ld79';
$SETTINGS["mysql_database"] = $current_db_name; // Use the selected or default database

$db = new mysqli($SETTINGS["hostname"], $SETTINGS["mysql_user"], $SETTINGS["mysql_pass"], $SETTINGS["mysql_database"]);

date_default_timezone_set('Asia/Riyadh');

if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
}

// Set charset to utf8mb4 for full Unicode support including emojis
if (!$db->set_charset("utf8mb4")) {
    die("Error loading character set utf8mb4: " . $db->error);
}

// Set the connection collation to handle emojis properly
$db->query("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
$db->query("SET CHARACTER SET utf8mb4");

// Function to sanitize content for database insertion
function sanitizeForDatabase($content) {
    // First, try to encode the content properly
    $clean_content = mb_convert_encoding($content, 'UTF-8', 'UTF-8');
    
    // If the content is not valid UTF-8, replace invalid sequences
    if (!mb_check_encoding($clean_content, 'UTF-8')) {
        $clean_content = mb_convert_encoding($content, 'UTF-8', 'auto');
    }
    
    // Only replace problematic control characters
    $clean_content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $clean_content);
    
    // Compress multiple consecutive blank lines into a single blank line
    $clean_content = preg_replace("/(?:\r?\n){3,}/", "\n\n", $clean_content);
    
    return $clean_content;
}

// Configure upload directory - use relative path from current script location
$uploadDir = '1comm/'; // Relative path for URLs
$serverUploadDir = __DIR__ . '/' . $uploadDir; // Absolute server path

// Debug information (remove in production)
if (isset($_FILES['file'])) {
    error_log("Upload attempt - Script directory: " . __DIR__);
    error_log("Upload directory path: " . $serverUploadDir);
    error_log("File upload error code: " . $_FILES['file']['error']);
}

// Handle file uploads
if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
    // Validate file upload
    $uploadError = '';
    $maxFileSize = 50 * 1024 * 1024; // 50MB max file size (increased for larger files)
    
    // Check file size
    if ($_FILES['file']['size'] > $maxFileSize) {
        $uploadError = "File size exceeds maximum allowed size of 50MB.";
    }
    
    // No file type restrictions - accept all files
    
    if (empty($uploadError)) {
        // Create upload directory if it doesn't exist
        if (!file_exists($serverUploadDir)) {
            // Check if parent directory is writable
            $parentDir = dirname($serverUploadDir);
            if (!is_writable($parentDir)) {
                $uploadError = "Parent directory is not writable: " . $parentDir;
            } else {
                if (!mkdir($serverUploadDir, 0755, true)) {
                    $error = error_get_last();
                    $uploadError = "Error creating upload directory: " . ($error ? $error['message'] : 'Permission denied') . " Path: " . $serverUploadDir;
                }
            }
        } elseif (!is_writable($serverUploadDir)) {
            $uploadError = "Upload directory exists but is not writable: " . $serverUploadDir;
        }
        
        if (empty($uploadError)) {
            // Generate unique filename to prevent overwrites
            $originalFileName = basename($_FILES['file']['name']);
            $fileName = time() . '_' . uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $originalFileName);
            $targetFile = $serverUploadDir . $fileName;
            
            // Debug information
            error_log("Original filename: " . $originalFileName);
            error_log("New filename: " . $fileName);
            error_log("Target file path: " . $targetFile);
            error_log("Temp file: " . $_FILES['file']['tmp_name']);
            error_log("Temp file exists: " . (file_exists($_FILES['file']['tmp_name']) ? 'yes' : 'no'));
            
            if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFile)) {
                // Verify file was actually moved
                if (!file_exists($targetFile)) {
                    $uploadError = "File was not saved to target location: " . $targetFile;
                } else {
                    // Store relative path in database
                    $content = $uploadDir . $fileName;
                    $fromUser = 'ttt';
                    $toUser = 'All';
                    $msgdate = date('Y-m-d H:i:s');
                    
                    // Pre-sanitize content to prevent emoji errors
                    $content = sanitizeForDatabase($content);
                    
                    error_log("Storing in database: " . $content);
                    
                    $stmt = $db->prepare("INSERT INTO 1comm (fromUser, toUser, msg, msgdate) VALUES (?, ?, ?, ?)");
                    if ($stmt) {
                        $stmt->bind_param("ssss", $fromUser, $toUser, $content, $msgdate);
                        if ($stmt->execute()) {
                            error_log("Database insert successful");
                            header("Location: " . $_SERVER['PHP_SELF']);
                            exit;
                        } else {
                            $uploadError = "Database error: " . $stmt->error;
                            // Clean up uploaded file on database error
                            if (file_exists($targetFile)) {
                                unlink($targetFile);
                            }
                        }
                        $stmt->close();
                    } else {
                        $uploadError = "Database prepare error: " . $db->error;
                        // Clean up uploaded file on database error
                        if (file_exists($targetFile)) {
                            unlink($targetFile);
                        }
                    }
                }
            } else {
                $uploadError = "Failed to move uploaded file. Check permissions on: " . $serverUploadDir;
                error_log("move_uploaded_file failed - Source: " . $_FILES['file']['tmp_name'] . " Target: " . $targetFile);
            }
        }
    }
    
    // Display error if any
    if (!empty($uploadError)) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 max-w-4xl mx-auto mt-4'>";
        echo htmlspecialchars($uploadError);
        echo "</div>";
    }
} elseif (isset($_FILES['file']) && $_FILES['file']['error'] !== UPLOAD_ERR_NO_FILE) {
    // Handle other upload errors
    $uploadErrors = [
        UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini.',
        UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form.',
        UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded.',
        UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder.',
        UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk.',
        UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload.'
    ];
    
    $errorMessage = isset($uploadErrors[$_FILES['file']['error']])
        ? $uploadErrors[$_FILES['file']['error']]
        : 'Unknown upload error.';
    
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 max-w-4xl mx-auto mt-4'>";
    echo htmlspecialchars($errorMessage);
    echo "</div>";
}

// Handle text message submissions
if (isset($_POST['content']) && !empty($_POST['content'])) {
    $content = $_POST['content'];
    $fromUser = 'ttt';
    $toUser = 'All';
    $msgdate = date('Y-m-d H:i:s');
    
    // Pre-sanitize content to prevent emoji errors
    $content = sanitizeForDatabase($content);
    
    $stmt = $db->prepare("INSERT INTO 1comm (fromUser, toUser, msg, msgdate) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $fromUser, $toUser, $content, $msgdate);
    $stmt->execute();
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

// Handle message deletion
if (isset($_POST['action']) && $_POST['action'] == 'delete' && isset($_POST['id'])) {
    $id = (int)$_POST['id'];
    $stmt = $db->prepare("DELETE FROM 1comm WHERE id = ?");
    $stmt->bind_param("i", $id);
    $result = $stmt->execute();
    
    // Return JSON response for AJAX request
    header('Content-Type: application/json');
    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => $db->error]);
    }
    exit;
}

// Fetch messages
$messages = $db->query("SELECT * FROM 1comm ORDER BY msgdate DESC");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- DOMPurify for sanitizing HTML & Marked for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.2/dist/purify.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body class="bg-gray-100 flex flex-col h-screen">
    <div class="flex-1 max-w-4xl mx-auto p-4 w-full flex flex-col overflow-hidden">

        <!-- Messages Container -->
        <div class="bg-white rounded-lg shadow-lg p-4 flex-1 overflow-y-auto flex flex-col-reverse border border-gray-200 relative" id="messagesContainer">
            <!-- Scroll to bottom floating button -->
            <div class="scroll-to-bottom" id="scrollToBottomBtn" title="Scroll to bottom">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
            </div>
            <?php $counter = 0; while($message = $messages->fetch_assoc()): ?>
                <div class="<?php echo $counter % 2 === 0 ? 'bg-gray-50' : 'bg-gray-100'; ?> rounded-lg p-4 mb-4 relative message-container group border-l-4 <?php echo $counter % 2 === 0 ? 'border-blue-400' : 'border-indigo-400'; ?>" style="transition: opacity 0.3s ease-in-out;">
                    <div class="sticky-buttons z-10">
                        <div class="flex flex-row gap-1">
                            <button 
                                onclick="copyFormattedContent(this)"
                                class="copy-btn p-1 bg-gray-200 bg-opacity-30 hover:bg-opacity-50 text-gray-700 border border-gray-600 rounded shadow-sm hover:shadow-md flex items-center justify-center"
                                title="Copy"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                            <button 
                                onclick="deleteMessage(this, <?php echo $message['id']; ?>)"
                                class="delete-btn p-1 bg-gray-200 bg-opacity-30 hover:bg-opacity-50 text-gray-700 border border-gray-600 rounded shadow-sm hover:shadow-md flex items-center justify-center"
                                title="Delete"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="message-content">
                        <?php
                        $content = ltrim($message['msg']);
                        $isImage = preg_match('/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i', $content);
                        // Check if content is a file path (relative path starting with upload directory)
                        $isFile = (strpos($content, $uploadDir) === 0 || strpos($content, 'http://icreditdept.online/1comm/') === 0);
                        
                        // Convert old absolute URLs to relative paths for consistency
                        if (strpos($content, 'http://icreditdept.online/1comm/') === 0) {
                            $content = str_replace('http://icreditdept.online/1comm/', $uploadDir, $content);
                        }
                        
                        // Determine file type for icon display
                        $fileIcon = 'M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z'; // Default folder icon
                        if ($isFile && !$isImage) {
                            $extension = strtolower(pathinfo($content, PATHINFO_EXTENSION));
                            switch($extension) {
                                case 'pdf':
                                    $fileIcon = 'M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z';
                                    break;
                                case 'doc':
                                case 'docx':
                                    $fileIcon = 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
                                    break;
                                case 'xls':
                                case 'xlsx':
                                    $fileIcon = 'M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z';
                                    break;
                                case 'zip':
                                case 'rar':
                                case '7z':
                                    $fileIcon = 'M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4';
                                    break;
                                case 'mp4':
                                case 'avi':
                                case 'mov':
                                case 'wmv':
                                    $fileIcon = 'M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z';
                                    break;
                                case 'mp3':
                                case 'wav':
                                case 'flac':
                                    $fileIcon = 'M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3';
                                    break;
                            }
                        }
                        ?>
                        <?php if(!$isImage && !$isFile): ?>
                            <div class="text-gray-800 break-words leading-relaxed text-base formatted-content whitespace-pre-wrap"><?php echo makeLinksClickable($content); ?></div>
                        <?php elseif($isImage): ?>
                            <img src="<?php echo htmlspecialchars($content); ?>" alt="Uploaded image" class="max-w-full h-auto" id="msg-image-<?php echo $counter; ?>">
                        <?php elseif($isFile): ?>
                            <?php $fileName = basename($content); ?>
                            <a href="<?php echo htmlspecialchars($content); ?>" 
                               class="flex items-center p-2 bg-gray-50 rounded-md text-blue-600 hover:bg-gray-100 break-all transition-colors duration-200 border border-gray-200 mb-2"
                               target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $fileIcon; ?>" />
                                </svg>
                                <?php echo htmlspecialchars($fileName); ?>
                            </a>
                        <?php endif; ?>
                        <div class="flex justify-between items-center mt-3">
                            <div class="text-xs font-medium text-gray-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <?php echo date('d/m/Y g:i A', strtotime($message['msgdate'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php $counter++; endwhile; ?>
        </div>

        <!-- Input Form -->
        <div class="bg-white rounded-lg shadow-md p-4 mt-4 sticky bottom-0">
            <form method="POST" enctype="multipart/form-data" class="space-y-4" id="messageForm">
                <!-- Add MAX_FILE_SIZE for PHP (50MB in bytes) -->
                <input type="hidden" name="MAX_FILE_SIZE" value="52428800" />
                <div
                    id="richTextArea"
                    class="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[4rem]"
                    contenteditable="true"
                    role="textbox"
                    aria-multiline="true"
                ></div>
                <textarea name="content" id="hiddenContent" class="hidden"></textarea>
                <div class="flex justify-between items-center">
                    <input
                        type="file"
                        name="file"
                        id="file"
                        class="hidden"
                        onchange="handleFileSelect(event)"
                    >
                    <label
                        for="file"
                        class="cursor-pointer bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-lg flex items-center"
                        title="Upload any file up to 50MB. Note: Folder uploads are not supported by browsers."
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                        Attach File
                    </label>
                    <button 
                        type="submit" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center"
                        onclick="submitForm(event)"
                    >
                        <span>Send</span>
                        <div class="loading-spinner ml-2 hidden">
                            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Delete message functionality
        async function deleteMessage(button, id) {
            if (confirm('Are you sure you want to delete this message?')) {
                try {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=delete&id=${id}`
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Remove the message from the DOM
                        const messageContainer = button.closest('.message-container');
                        messageContainer.style.opacity = '0';
                        setTimeout(() => {
                            messageContainer.remove();
                        }, 300);
                    } else {
                        alert('Error deleting message: ' + (result.error || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Error deleting message. Please try again.');
                }
            }
        }
        
        // Copy functionality for formatted content
        async function copyFormattedContent(button) {
            // Get the message container that contains this button
            const messageContainer = button.closest('.message-container');
            const messageContent = messageContainer.querySelector('.message-content');
            
            // Save original button content
            const originalContent = button.innerHTML;
            
            // Get the content based on what type of content it is
            const image = messageContent.querySelector('img');
            const link = messageContent.querySelector('a');
            const formattedContent = messageContent.querySelector('.formatted-content');
            const textContent = messageContent.querySelector('.text-gray-800');
            
            let success = false;
            
            try {
                if (image) {
                    // Copy image as actual image to clipboard (not just URL)
                    try {
                        // Create a canvas element
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        // Wait for the image to load
                        await new Promise((resolve, reject) => {
                            // Create a new image to ensure it's fully loaded
                            const img = new Image();
                            img.crossOrigin = 'anonymous';  // Try to avoid CORS issues
                            img.onload = () => {
                                // Set canvas dimensions to match the image
                                canvas.width = img.width;
                                canvas.height = img.height;
                                
                                // Draw the image on the canvas
                                ctx.drawImage(img, 0, 0);
                                resolve();
                            };
                            img.onerror = () => {
                                // Fallback to copying URL if image loading fails
                                reject();
                            };
                            img.src = image.src;
                        });
                        
                        // Get the image data as a blob
                        canvas.toBlob(async (blob) => {
                            // Check if the Clipboard API supports ClipboardItem
                            if (window.ClipboardItem) {
                                // Create a ClipboardItem
                                const item = new ClipboardItem({ 'image/png': blob });
                                
                                // Write the image to clipboard
                                await navigator.clipboard.write([item]);
                            } else {
                                // Fallback for browsers that don't support ClipboardItem
                                // Create a temporary link to download the image
                                const url = URL.createObjectURL(blob);
                                await navigator.clipboard.writeText("Image: " + image.src);
                                console.warn('Your browser does not support copying images directly. The image URL has been copied instead.');
                            }
                            success = true;
                            
                            // Update button immediately since this is in a callback
                            button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>Image Copied!';
                            
                            // Reset button after 2 seconds
                            setTimeout(() => {
                                button.innerHTML = originalContent;
                            }, 2000);
                        });
                    } catch (err) {
                        // Fallback to copying the URL if the image copy fails
                        console.error('Failed to copy image, falling back to URL:', err);
                        await navigator.clipboard.writeText(image.src);
                        success = true;
                    }
                } else if (link) {
                    // Copy link URL
                    await navigator.clipboard.writeText(link.href);
                    success = true;
                } else if (formattedContent || textContent) {
                    // Use selection API for rich text
                    const element = formattedContent || textContent;
                    
                    // Create a selection range
                    const selection = window.getSelection();
                    const range = document.createRange();
                    range.selectNodeContents(element);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    // Execute copy command
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    success = true;
                }
            } catch (err) {
                console.error('Copy failed:', err);
                button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>Copy failed!';
            }
            
            if (success) {
                // Update button text with success message
                button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>Copied!';
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalContent;
                }, 2000);
            }
        }

        const messagesContainer = document.getElementById('messagesContainer');
        
        // Scroll to bottom functionality
        document.getElementById('scrollToBottomBtn').addEventListener('click', function() {
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.scrollTop = 0; // Since it's flex-col-reverse, 0 is the bottom
        });
        
        // Make sure the button is always visible
        document.getElementById('scrollToBottomBtn').style.display = 'flex';
        
        // Optional: Hide the button when at the bottom (commented out for now)
        /*
        messagesContainer.addEventListener('scroll', function() {
            const scrollButton = document.getElementById('scrollToBottomBtn');
            // Show button when scrolled up (since it's flex-col-reverse)
            if (this.scrollTop > 100) {
                scrollButton.style.display = 'flex';
            } else {
                scrollButton.style.display = 'none';
            }
        });
        */
        
        // Handle file selection with validation
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            // Validate file size (50MB max)
            const maxSize = 50 * 1024 * 1024; // 50MB in bytes
            if (file.size > maxSize) {
                alert('File size exceeds 50MB limit. Please choose a smaller file.');
                event.target.value = ''; // Clear the file input
                return;
            }
            
            // No file type validation - accept all files
            
            // If validation passes, submit the form
            submitForm(event);
        }
        
        // Form submission with loading indicator
        function submitForm(event) {
            event.preventDefault();
            const form = document.getElementById('messageForm');
            const submitButton = form.querySelector('button[type="submit"]');
            const spinner = submitButton.querySelector('.loading-spinner');
            const buttonText = submitButton.querySelector('span');
            const fileInput = document.getElementById('file');
            
            // Update hidden textarea with formatted content
            const richTextArea = document.getElementById('richTextArea');
            const hiddenContent = document.getElementById('hiddenContent');
            hiddenContent.value = richTextArea.innerHTML;
            
            // Show loading state
            spinner.classList.remove('hidden');
            buttonText.textContent = 'Sending...';
            submitButton.disabled = true;
            
            // Check if a file has been selected for upload
            if (fileInput.files.length > 0) {
                // Handle file upload
                const formData = new FormData(form);
                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                }).then(() => {
                    window.location.reload();
                }).catch(error => {
                    console.error('Error:', error);
                    alert('Error uploading file. Please try again.');
                    // Reset loading state on error
                    spinner.classList.add('hidden');
                    buttonText.textContent = 'Send';
                    submitButton.disabled = false;
                    // Clear file input
                    fileInput.value = '';
                });
            } else {
                // Handle regular form submission
                form.submit();
            }
        }

        // Rich text area paste handler
        document.getElementById('richTextArea').addEventListener('paste', function(e) {
            e.preventDefault();
            
            // Handle image paste
            const items = e.clipboardData.items;
            for (let i = 0; i <items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const file = items[i].getAsFile();
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const submitButton = document.querySelector('button[type="submit"]');
                    const spinner = submitButton.querySelector('.loading-spinner');
                    const buttonText = submitButton.querySelector('span');
                    
                    spinner.classList.remove('hidden');
                    buttonText.textContent = 'Sending...';
                    submitButton.disabled = true;
                    
                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    }).then(() => {
                        window.location.reload();
                    }).catch(error => {
                        console.error('Error:', error);
                        spinner.classList.add('hidden');
                        buttonText.textContent = 'Send';
                        submitButton.disabled = false;
                    });
                    
                    return;
                }
            }
            
            // Handle formatted or plain text paste with support for rich formats & Markdown
            const html = e.clipboardData.getData('text/html');
            const text = e.clipboardData.getData('text/plain');

            const ALLOWED_TAGS = ['b','i','strong','em','u','s','br','p','ul','ol','li','span','div','img','a','table','thead','tbody','tr','td','th','code','pre','blockquote','hr'];
            const ALLOWED_ATTR = ['href','src','target','style'];

            if (html) {
                // Sanitize and insert HTML clipboard data
                const sanitizedHtml = DOMPurify.sanitize(html, {ALLOWED_TAGS, ALLOWED_ATTR});
                document.execCommand('insertHTML', false, sanitizedHtml);
            } else if (text) {
                // Detect basic Markdown patterns (e.g., **bold**, _italic_, lists, headings)
                const markdownPattern = /[*_`#\-]{1,}|\n-{3,}|\n\s*\d+\.\s+/;
                if (markdownPattern.test(text)) {
                    // Convert Markdown to HTML then sanitize
                    let mdHtml = marked.parse(text);
                    mdHtml = DOMPurify.sanitize(mdHtml, {ALLOWED_TAGS, ALLOWED_ATTR});
                    document.execCommand('insertHTML', false, mdHtml);
                } else {
                    // Plain text: preserve line breaks
                    let cleanedText = text.replace(/\u00A0/g, ' ');
                    cleanedText = cleanedText.replace(/\r\n?/g, '\n');
                    cleanedText = cleanedText.replace(/\n{3,}/g, '\n\n');
                    cleanedText = cleanedText.trimEnd();
                    const htmlText = cleanedText.replace(/\n/g, '<br>');
                    const sanitizedText = DOMPurify.sanitize(htmlText, {ALLOWED_TAGS, ALLOWED_ATTR});
                    document.execCommand('insertHTML', false, sanitizedText);
                }
            }
        });

        // Handle Ctrl+Enter submission
        document.getElementById('richTextArea').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                submitForm(e);
            }
        });
    </script>

    <style>
        html, body {
            height: 100%;
            scroll-behavior: smooth;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .sticky-buttons {
            position: sticky;
            top: 0;
            float: right;
            margin-right: -8px;  /* Offset the parent padding */
            height: 0;          /* Don't take up vertical space */
            overflow: visible;  /* Allow buttons to be visible */
        }
        
        #richTextArea {
            white-space: pre-wrap;
            min-height: 4rem;
            overflow-y: auto;
        }
        .copy-btn {
            /* Position is now sticky, top-2, right-2 defined inline */
        }
        /* Remove default paragraph margins inside message content */
        .message-content p {
            margin: 0;
        }
            /* Scroll to bottom button styles */
        .scroll-to-bottom {
            position: fixed;
            bottom: 168px; /* Align with top of input form */
            right: calc(((max(0px, 100vw - 56rem)) / 2) + 36px); /* Position right edge 36px from main content block right edge */
            background-color: rgba(59, 130, 246, 0.8); /* blue-500 with opacity */
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: all 0.3s ease;
        }
        
        .scroll-to-bottom:hover {
            background-color: rgba(37, 99, 235, 0.9); /* blue-600 with opacity */
            transform: scale(1.1);
        }
        
        .scroll-to-bottom svg {
            width: 20px;
            height: 20px;
        }
    </style>
</body>
</html>