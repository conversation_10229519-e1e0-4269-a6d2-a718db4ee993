{"id": "lYOQGMEJDxugrfrT", "meta": {"instanceId": "6d46e25379ef430a7067964d1096b885c773564549240cb3ad4c087f6cf94bd3", "templateCredsSetupCompleted": true}, "name": "MCP_GMAIL", "tags": [], "nodes": [{"id": "c13105ec-6ac3-4179-a331-da5214ced6e6", "name": "SEND_EMAIL", "type": "n8n-nodes-base.gmailTool", "position": [-260, 540], "webhookId": "7ecc72c7-8968-4e5c-ae5a-a3d41823ca73", "parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "descriptionType": "manual", "toolDescription": "envia menssagems via API google"}, "credentials": {"gmailOAuth2": {"id": "qvvveZOGtqMK0yvU", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "0836faf7-59fd-41f5-9a54-3f467ed87db1", "name": "REPLY_EMAIL", "type": "n8n-nodes-base.gmailTool", "position": [-120, 540], "webhookId": "30c6020e-eceb-4381-9874-be21f34ceea2", "parameters": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "reply", "descriptionType": "manual"}, "credentials": {"gmailOAuth2": {"id": "qvvveZOGtqMK0yvU", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "892e89a7-8751-4c9f-87f3-a663ec0ca042", "name": "GET_EMAIL", "type": "n8n-nodes-base.gmailTool", "position": [0, 540], "webhookId": "6b3f4e85-bdfe-4396-8ee7-5a73efc680fb", "parameters": {"simple": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Simplify', ``, 'boolean') }}", "options": {}, "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "qvvveZOGtqMK0yvU", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "c1c2d8cd-6117-4238-b696-22da8f217f59", "name": "SEND_AND_WAIT", "type": "n8n-nodes-base.gmailTool", "position": [140, 540], "webhookId": "1f8a82c1-cbd1-4f40-9820-2de005c0473f", "parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}, "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "operation": "sendAndWait", "responseType": "freeText"}, "credentials": {"gmailOAuth2": {"id": "qvvveZOGtqMK0yvU", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "89c8bc41-cbf9-4099-a3ac-6d5d7cd1a626", "name": "MCP_GMAIL", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [0, 0], "webhookId": "82a7a338-618c-44f5-a1c3-f2e32b6b4833", "parameters": {"path": "/mcp/:tool/gmail"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2022464f-822f-4b3e-9425-4e938a95348c", "connections": {"GET_EMAIL": {"ai_tool": [[{"node": "MCP_GMAIL", "type": "ai_tool", "index": 0}]]}, "SEND_EMAIL": {"ai_tool": [[{"node": "MCP_GMAIL", "type": "ai_tool", "index": 0}]]}, "REPLY_EMAIL": {"ai_tool": [[{"node": "MCP_GMAIL", "type": "ai_tool", "index": 0}]]}, "SEND_AND_WAIT": {"ai_tool": [[{"node": "MCP_GMAIL", "type": "ai_tool", "index": 0}]]}}}