<?php

session_start(); // Start the session

// Set the database name
$current_db_name = 'icredept_1comm'; // Default database

// Function to make URLs in text clickable
function makeLinksClickable($text) {
    // Regular expression to match URLs
    $pattern = '/(https?:\/\/[\w\-\.\/?\&%=\+\~\#\:;\,\!\@]+)/i';
    
    // Replace URLs with anchor tags
    $replacement = '<a href="$1" target="_blank" class="text-blue-600 hover:underline break-all">$1</a>';
    
    // Perform the replacement
    return preg_replace($pattern, $replacement, $text);
}

// Prevent browser caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Database connection
$SETTINGS["hostname"] = '*************';
$SETTINGS["mysql_user"] = 'tariq';
$SETTINGS["mysql_pass"] = 'Hgd!ld79';
$SETTINGS["mysql_database"] = $current_db_name; // Use the selected or default database

$db = new mysqli($SETTINGS["hostname"], $SETTINGS["mysql_user"], $SETTINGS["mysql_pass"], $SETTINGS["mysql_database"]);

date_default_timezone_set('Asia/Riyadh');

if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
}

$uploadDir = 'http://icreditdept.online/1comm/';
$serverUploadDir = $_SERVER['DOCUMENT_ROOT'] . '/1comm/';

// Handle file uploads
if (isset($_FILES['file'])) {
    if (!file_exists($serverUploadDir)) {
        mkdir($serverUploadDir, 0777, true);
    }
    
    $fileName = time() . '_' . basename($_FILES['file']['name']);
    $targetFile = $serverUploadDir . $fileName;
    
    if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFile)) {
        $content = $uploadDir . $fileName;
        $fromUser = 'ttt';
        $toUser = 'All';
        $msgdate = date('Y-m-d H:i:s');
        $stmt = $db->prepare("INSERT INTO 1comm (fromUser, toUser, msg, msgdate) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $fromUser, $toUser, $content, $msgdate);
        $stmt->execute();
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Handle text message submissions
if (isset($_POST['content']) && !empty($_POST['content'])) {
    $content = $_POST['content'];
    $fromUser = 'ttt';
    $toUser = 'All';
    $msgdate = date('Y-m-d H:i:s');
    $stmt = $db->prepare("INSERT INTO 1comm (fromUser, toUser, msg, msgdate) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $fromUser, $toUser, $content, $msgdate);
    $stmt->execute();
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

// Handle message deletion
if (isset($_POST['action']) && $_POST['action'] == 'delete' && isset($_POST['id'])) {
    $id = (int)$_POST['id'];
    $stmt = $db->prepare("DELETE FROM 1comm WHERE id = ?");
    $stmt->bind_param("i", $id);
    $result = $stmt->execute();
    
    // Return JSON response for AJAX request
    header('Content-Type: application/json');
    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => $db->error]);
    }
    exit;
}

// Fetch messages
$messages = $db->query("SELECT * FROM 1comm ORDER BY msgdate DESC");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes App</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex flex-col h-screen">
    <div class="flex-1 max-w-4xl mx-auto p-4 w-full flex flex-col overflow-hidden">

        <!-- Messages Container -->
        <div class="bg-white rounded-lg shadow-lg p-4 flex-1 overflow-y-auto flex flex-col-reverse border border-gray-200 relative" id="messagesContainer">
            <!-- Scroll to bottom floating button -->
            <div class="scroll-to-bottom" id="scrollToBottomBtn" title="Scroll to bottom">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
            </div>
            <?php $counter = 0; while($message = $messages->fetch_assoc()): ?>
                <div class="<?php echo $counter % 2 === 0 ? 'bg-gray-50' : 'bg-gray-100'; ?> rounded-lg p-4 mb-4 relative message-container group border-l-4 <?php echo $counter % 2 === 0 ? 'border-blue-400' : 'border-indigo-400'; ?>" style="transition: opacity 0.3s ease-in-out;">
                    <div class="sticky-buttons z-10">
                        <div class="flex flex-row gap-1">
                            <button 
                                onclick="copyFormattedContent(this)"
                                class="copy-btn p-1 bg-gray-200 bg-opacity-30 hover:bg-opacity-50 text-gray-700 border border-gray-600 rounded shadow-sm hover:shadow-md flex items-center justify-center"
                                title="Copy"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                            <button 
                                onclick="deleteMessage(this, <?php echo $message['id']; ?>)"
                                class="delete-btn p-1 bg-gray-200 bg-opacity-30 hover:bg-opacity-50 text-gray-700 border border-gray-600 rounded shadow-sm hover:shadow-md flex items-center justify-center"
                                title="Delete"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="message-content">
                        <?php
                        $content = ltrim($message['msg']);
                        $isImage = preg_match('/\.(jpg|jpeg|png|gif)$/i', $content);
                        $isFile = strpos($content, $uploadDir) === 0;
                        ?>
                        <?php if(!$isImage && !$isFile): ?>
                            <div class="text-gray-800 break-words leading-relaxed text-base formatted-content whitespace-pre-wrap"><?php echo makeLinksClickable($content); ?></div>
                        <?php elseif($isImage): ?>
                            <img src="<?php echo htmlspecialchars($content); ?>" alt="Uploaded image" class="max-w-full h-auto" id="msg-image-<?php echo $counter; ?>">
                        <?php elseif($isFile): ?>
                            <?php $fileName = basename($content); ?>
                            <a href="<?php echo htmlspecialchars($content); ?>" 
                               class="flex items-center p-2 bg-gray-50 rounded-md text-blue-600 hover:bg-gray-100 break-all transition-colors duration-200 border border-gray-200 mb-2"
                               target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                                </svg>
                                <?php echo htmlspecialchars($fileName); ?>
                            </a>
                        <?php endif; ?>
                        <div class="flex justify-between items-center mt-3">
                            <div class="text-xs font-medium text-gray-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <?php echo date('d/m/Y g:i A', strtotime($message['msgdate'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php $counter++; endwhile; ?>
        </div>

        <!-- Input Form -->
        <div class="bg-white rounded-lg shadow-md p-4 mt-4 sticky bottom-0">
            <form method="POST" enctype="multipart/form-data" class="space-y-4" id="messageForm">
                <div 
                    id="richTextArea"
                    class="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[4rem]"
                    contenteditable="true"
                    role="textbox"
                    aria-multiline="true"
                ></div>
                <textarea name="content" id="hiddenContent" class="hidden"></textarea>
                <div class="flex justify-between items-center">
                    <input 
                        type="file" 
                        name="file" 
                        id="file" 
                        class="hidden"
                        onchange="submitForm(event)"
                    >
                    <label 
                        for="file" 
                        class="cursor-pointer bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-lg"
                    >
                        Attach File
                    </label>
                    <button 
                        type="submit" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center"
                        onclick="submitForm(event)"
                    >
                        <span>Send</span>
                        <div class="loading-spinner ml-2 hidden">
                            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Delete message functionality
        async function deleteMessage(button, id) {
            if (confirm('Are you sure you want to delete this message?')) {
                try {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=delete&id=${id}`
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Remove the message from the DOM
                        const messageContainer = button.closest('.message-container');
                        messageContainer.style.opacity = '0';
                        setTimeout(() => {
                            messageContainer.remove();
                        }, 300);
                    } else {
                        alert('Error deleting message: ' + (result.error || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Error deleting message. Please try again.');
                }
            }
        }
        
        // Copy functionality for formatted content
        async function copyFormattedContent(button) {
            // Get the message container that contains this button
            const messageContainer = button.closest('.message-container');
            const messageContent = messageContainer.querySelector('.message-content');
            
            // Save original button content
            const originalContent = button.innerHTML;
            
            // Get the content based on what type of content it is
            const image = messageContent.querySelector('img');
            const link = messageContent.querySelector('a');
            const formattedContent = messageContent.querySelector('.formatted-content');
            const textContent = messageContent.querySelector('.text-gray-800');
            
            let success = false;
            
            try {
                if (image) {
                    // Copy image as actual image to clipboard (not just URL)
                    try {
                        // Create a canvas element
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        // Wait for the image to load
                        await new Promise((resolve, reject) => {
                            // Create a new image to ensure it's fully loaded
                            const img = new Image();
                            img.crossOrigin = 'anonymous';  // Try to avoid CORS issues
                            img.onload = () => {
                                // Set canvas dimensions to match the image
                                canvas.width = img.width;
                                canvas.height = img.height;
                                
                                // Draw the image on the canvas
                                ctx.drawImage(img, 0, 0);
                                resolve();
                            };
                            img.onerror = () => {
                                // Fallback to copying URL if image loading fails
                                reject();
                            };
                            img.src = image.src;
                        });
                        
                        // Get the image data as a blob
                        canvas.toBlob(async (blob) => {
                            // Check if the Clipboard API supports ClipboardItem
                            if (window.ClipboardItem) {
                                // Create a ClipboardItem
                                const item = new ClipboardItem({ 'image/png': blob });
                                
                                // Write the image to clipboard
                                await navigator.clipboard.write([item]);
                            } else {
                                // Fallback for browsers that don't support ClipboardItem
                                // Create a temporary link to download the image
                                const url = URL.createObjectURL(blob);
                                await navigator.clipboard.writeText("Image: " + image.src);
                                console.warn('Your browser does not support copying images directly. The image URL has been copied instead.');
                            }
                            success = true;
                            
                            // Update button immediately since this is in a callback
                            button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>Image Copied!';
                            
                            // Reset button after 2 seconds
                            setTimeout(() => {
                                button.innerHTML = originalContent;
                            }, 2000);
                        });
                    } catch (err) {
                        // Fallback to copying the URL if the image copy fails
                        console.error('Failed to copy image, falling back to URL:', err);
                        await navigator.clipboard.writeText(image.src);
                        success = true;
                    }
                } else if (link) {
                    // Copy link URL
                    await navigator.clipboard.writeText(link.href);
                    success = true;
                } else if (formattedContent || textContent) {
                    // Use selection API for rich text
                    const element = formattedContent || textContent;
                    
                    // Create a selection range
                    const selection = window.getSelection();
                    const range = document.createRange();
                    range.selectNodeContents(element);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    // Execute copy command
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    success = true;
                }
            } catch (err) {
                console.error('Copy failed:', err);
                button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>Copy failed!';
            }
            
            if (success) {
                // Update button text with success message
                button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>Copied!';
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalContent;
                }, 2000);
            }
        }

        const messagesContainer = document.getElementById('messagesContainer');
        
        // Scroll to bottom functionality
        document.getElementById('scrollToBottomBtn').addEventListener('click', function() {
            const messagesContainer = document.getElementById('messagesContainer');
            messagesContainer.scrollTop = 0; // Since it's flex-col-reverse, 0 is the bottom
        });
        
        // Make sure the button is always visible
        document.getElementById('scrollToBottomBtn').style.display = 'flex';
        
        // Optional: Hide the button when at the bottom (commented out for now)
        /*
        messagesContainer.addEventListener('scroll', function() {
            const scrollButton = document.getElementById('scrollToBottomBtn');
            // Show button when scrolled up (since it's flex-col-reverse)
            if (this.scrollTop > 100) {
                scrollButton.style.display = 'flex';
            } else {
                scrollButton.style.display = 'none';
            }
        });
        */
        
        // Form submission with loading indicator
        function submitForm(event) {
            event.preventDefault();
            const form = document.getElementById('messageForm');
            const submitButton = form.querySelector('button[type="submit"]');
            const spinner = submitButton.querySelector('.loading-spinner');
            const buttonText = submitButton.querySelector('span');
            
            // Update hidden textarea with formatted content
            const richTextArea = document.getElementById('richTextArea');
            const hiddenContent = document.getElementById('hiddenContent');
            hiddenContent.value = richTextArea.innerHTML;
            
            // Show loading state
            spinner.classList.remove('hidden');
            buttonText.textContent = 'Sending...';
            submitButton.disabled = true;
            
            if (event.target.type === 'file') {
                // Handle file upload
                const formData = new FormData(form);
                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                }).then(() => {
                    window.location.reload();
                }).catch(error => {
                    console.error('Error:', error);
                    // Reset loading state on error
                    spinner.classList.add('hidden');
                    buttonText.textContent = 'Send';
                    submitButton.disabled = false;
                });
            } else {
                // Handle regular form submission
                form.submit();
            }
        }

        // Rich text area paste handler
        document.getElementById('richTextArea').addEventListener('paste', function(e) {
            e.preventDefault();
            
            // Handle image paste
            const items = e.clipboardData.items;
            for (let i = 0; i <items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const file = items[i].getAsFile();
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const submitButton = document.querySelector('button[type="submit"]');
                    const spinner = submitButton.querySelector('.loading-spinner');
                    const buttonText = submitButton.querySelector('span');
                    
                    spinner.classList.remove('hidden');
                    buttonText.textContent = 'Sending...';
                    submitButton.disabled = true;
                    
                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    }).then(() => {
                        window.location.reload();
                    }).catch(error => {
                        console.error('Error:', error);
                        spinner.classList.add('hidden');
                        buttonText.textContent = 'Send';
                        submitButton.disabled = false;
                    });
                    
                    return;
                }
            }
            
            // Handle formatted text paste
            const html = e.clipboardData.getData('text/html');
            const text = e.clipboardData.getData('text/plain');
            
            if (html) {
                // Clean and sanitize HTML content
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // Remove potentially harmful elements and attributes
                const sanitizedHtml = tempDiv.innerHTML;
                document.execCommand('insertHTML', false, sanitizedHtml);
            } else {
                document.execCommand('insertText', false, text);
            }
        });

        // Handle Ctrl+Enter submission
        document.getElementById('richTextArea').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                submitForm(e);
            }
        });
    </script>

    <style>
        html, body {
            height: 100%;
            scroll-behavior: smooth;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .sticky-buttons {
            position: sticky;
            top: 0;
            float: right;
            margin-right: -8px;  /* Offset the parent padding */
            height: 0;          /* Don't take up vertical space */
            overflow: visible;  /* Allow buttons to be visible */
        }
        
        #richTextArea {
            white-space: pre-wrap;
            min-height: 4rem;
            overflow-y: auto;
        }
        .copy-btn {
            /* Position is now sticky, top-2, right-2 defined inline */
        }
        /* Remove default paragraph margins inside message content */
        .message-content p {
            margin: 0;
        }
            /* Scroll to bottom button styles */
        .scroll-to-bottom {
            position: fixed;
            bottom: 168px; /* Align with top of input form */
            right: calc(((max(0px, 100vw - 56rem)) / 2) + 36px); /* Position right edge 36px from main content block right edge */
            background-color: rgba(59, 130, 246, 0.8); /* blue-500 with opacity */
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: all 0.3s ease;
        }
        
        .scroll-to-bottom:hover {
            background-color: rgba(37, 99, 235, 0.9); /* blue-600 with opacity */
            transform: scale(1.1);
        }
        
        .scroll-to-bottom svg {
            width: 20px;
            height: 20px;
        }
    </style>
</body>
</html>