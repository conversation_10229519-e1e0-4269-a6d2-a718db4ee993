{"id": "nV1xFcF5HWJcD6w7", "meta": {"instanceId": "b1be0f8fadff87de92fbcd08be474fb794e544ef8a62dd9c586c9914a3836990"}, "name": "Automatically Send Daily Meeting List to Telegram", "tags": [{"id": "THCdGkSMWvR7AzSR", "name": "Template", "createdAt": "2024-02-28T08:32:57.511Z", "updatedAt": "2024-02-28T08:32:57.511Z"}, {"id": "ro6MmCu2eov1eWfR", "name": "Creators", "createdAt": "2024-03-01T11:11:36.214Z", "updatedAt": "2024-03-01T11:11:36.214Z"}], "nodes": [{"id": "eee04fe7-7f65-4db8-8ad8-7b67197a1f70", "name": "Get meetings for today", "type": "n8n-nodes-base.googleCalendar", "position": [1240, 580], "parameters": {"options": {"timeMax": "={{ $today.plus({ days: 1 }) }}", "timeMin": "={{ $today }}", "singleEvents": true}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "Meeting Room"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "BSvdyVkCIqvVagsr", "name": "Google Calendar account"}}, "typeVersion": 1}, {"id": "358ab462-d69f-4980-99fd-de5a22a3c783", "name": "Every morning @ 6", "type": "n8n-nodes-base.scheduleTrigger", "position": [940, 580], "parameters": {"rule": {"interval": [{"triggerAtHour": 6}]}}, "typeVersion": 1.1}, {"id": "57f77b4e-d608-4929-bc49-2dfecff88c8d", "name": "Set", "type": "n8n-nodes-base.set", "position": [1520, 580], "parameters": {"values": {"number": [], "string": [{"name": "Name", "value": "={{ $json.summary }}"}, {"name": "Time", "value": "={{ $json.start }}"}, {"name": "Guests", "value": "={{ $json.attendees }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "6bcde2e8-46f6-46aa-b2f2-0e2670a9ce66", "name": "Function", "type": "n8n-nodes-base.function", "position": [1780, 580], "parameters": {"functionCode": "let message = \"*Your meetings today are:* \\n\";\n\nfor (item of items) {\n  const time = new Date(item.json.Time.dateTime);\n  const formattedTime = new Intl.DateTimeFormat('fa-IR', {\n    hour: 'numeric',\n    minute: 'numeric',\n    timeZone: item.json.Time.timeZone\n  }).format(time);\n\n  message += `* ${item.json.Name} | ${formattedTime}\\n`;\n\n  if (item.json.Guests && item.json.Guests.length > 0) {\n    message += '*Â&nbsp;- ';\n    item.json.Guests.forEach((guest, index) => {\n      message += `${guest.email}${index < item.json.Guests.length - 1 ? ', ' : ''}`;\n    });\n    message += '\\n';\n  } else {\n    message += '*Â&nbsp;- No guests\\n';\n  }\n}\n\nreturn [{ json: { message } }];\n"}, "typeVersion": 1}, {"id": "568c4efd-a4d4-4309-ab3e-c15c955ce361", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [2120, 580], "parameters": {"text": "={{$json[\"message\"]}}", "additionalFields": {}}, "typeVersion": 1.1}, {"id": "9f2b0543-9f3f-43e2-a7ea-e77ce1430985", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [800, 232], "parameters": {"color": 7, "width": 1527.817454565021, "height": 658.1528835709971, "content": "## This workflow \nprovides a convenient and automated way to stay on top of your daily meetings and improve your personal productivity.\n\n\n"}, "typeVersion": 1}, {"id": "41d85383-ccca-42f6-b9a1-d18e14ab3e32", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2031.7098362416477, 431.96581702471417], "parameters": {"color": 5, "width": 268.2901637583533, "height": 315.7841809336307, "content": "### Create a Telegram bot in @botfather\nUses your Telegram user ID to send the list of meetings as a message to Telegram."}, "typeVersion": 1}, {"id": "254dccf8-a366-4cdc-84ca-987eca928ed6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [820, 340], "parameters": {"width": 430.6727493433055, "height": 151.60560223016907, "content": "## setup:\n###    - Google Calendar connected to n8n\n###    - A Telegram bot created and connected to n8n\n###    - Your Telegram user ID specified"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Tehran", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true}, "versionId": "9dc21ef6-2b7d-4c80-9c03-0d636ab6f0d1", "connections": {"Set": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Every morning @ 6": {"main": [[{"node": "Get meetings for today", "type": "main", "index": 0}]]}, "Get meetings for today": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}