<?php
header('Content-Type: application/json');
// Only allow specific queries or whitelist this script by IP/basic‐auth in real use!
$q = $_GET['q'] ?? 'SELECT * from 1comm limit 2';

$SETTINGS["hostname"] = '************';
$SETTINGS["mysql_user"] = 'icredept_tariq';
$SETTINGS["mysql_pass"] = 'Hgd!ld198479';
$SETTINGS["mysql_database"] = 'icredept_1comm';


try {
  $pdo = new PDO('mysql:host=************;dbname=icredept_1comm;charset=utf8', 'icredept_tariq', 'Hgd!ld198479', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
  ]);
  $rows = $pdo->query($q)->fetchAll(PDO::FETCH_ASSOC);
  echo json_encode($rows);
} catch (PDOException $e) {
  http_response_code(500);
  echo json_encode(['error' => $e->getMessage()]);
}
