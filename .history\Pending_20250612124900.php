<?php
ob_start();
include('connect.php');     //  <-- supplies $con  (mysqli)
include('session.php');     //  <-- supplies $session_id
session_start();
$username = $session_id;
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://static.pingendo.com/bootstrap/bootstrap-4.3.1.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@10/dist/sweetalert2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10/dist/sweetalert2.min.js"></script>
<title>Pending</title>
<style>
/* --------- basic look --------- */
table{border-collapse:collapse;font-family:Calibri,Arial,Helvetica;font-size:17px}
th,td{padding:6px 8px;border:1px solid #e1e1e1;white-space:normal;word-wrap:break-word}
tr:nth-child(even){background:#fafafa}

/* --------- elegant palette --------- */
.receivedhead{background:#0d6efd;color:#fff}   /* blue header  */
.currenthead {background:#6c757d;color:#fff}   /* gray header  */
.received    {background:#e9f5ff}              /* subtle blue  */
.current     {background:#f5f5f5}              /* subtle gray  */

/* page utilities */
.btn-primary1,.btn-primary1:hover,.btn-primary1:focus,.btn-primary1:active,
.btn-primary1:focus-within{border-color:#B3892F;background-color:#DDA0DD;color:#000}
.dropdown-toggle:hover,.dropdown-toggle:focus,.dropdown-toggle:active,
.dropdown-toggle:focus-within{border-color:#B3892F;background-color:#DDA0DD;color:#000}
.input-container{display:inline-block}.left{float:left}.right{float:right}
</style>

<script>
/* --- Excel export (kept simple) --- */
var tableToExcel=(function(){var uri='data:application/vnd.ms-excel;base64,',
template='<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
b64=s=>window.btoa(unescape(encodeURIComponent(s))),
fmt=(s,c)=>s.replace(/{(\w+)}/g,(m,p)=>c[p]);
return function(table,name){
    if(!table.nodeType) table=document.getElementById(table);
    let copy=table.cloneNode(true);
    copy.querySelectorAll('tr').forEach(r=>r.deleteCell(0));        // strip serial
    copy.querySelectorAll('a').forEach(a=>a.outerHTML=a.textContent);
    let ctx={worksheet:name||'Sheet',table:copy.innerHTML};
    window.location.href=uri+b64(fmt(template,ctx));
}})();

/* --- search helper --- */
function searchTable(){
  const term=document.getElementById("search").value.toUpperCase();
  document.querySelectorAll("#dataTable tr").forEach(tr=>{
     tr.style.display=tr.innerText.toUpperCase().includes(term)?'':'none';
  });
}
</script>
</head>
<body>
<?php
$result2=mysqli_query($con,'SELECT Screen FROM UserScreens WHERE username="'.$username.'" ORDER BY Screen');
?>
<div class="py-5">
  <div class="container">
    <div class="row">
      <div class="col-md-4"></div>
      <div class="col-md-4 text-center"><h1 class="display-5">Pending1</h1></div>
      <div class="col-md-4 text-left pt-2">
      <?php if(mysqli_num_rows($result2)>1){ ?>
        <div class="btn-group">
          <button class="btn btn-primary1 dropdown-toggle" data-toggle="dropdown">Go to another screen</button>
          <div class="dropdown-menu">
            <?php while($row=$result2->fetch_assoc()){echo "<a class='dropdown-item' href='".$row['Screen'].".php'>".$row['Screen']."</a>";} ?>
          </div>
        </div>
      <?php } ?>
      </div>
    </div>
  </div>
</div>

<?php
$pendingdate=date("Y-m-d",strtotime("-1 day"));
if(isset($_POST['submit'])) $pendingdate=$_POST['pendingdate'];
?>
<div style="text-align:center">
<form method="post">
  <label>Choose a date:</label>
  <input type="date" name="pendingdate" value="<?php echo $pendingdate;?>">
  <input type="submit" name="submit" value="Submit">
</form>
</div>
<hr>

<?php
/* -------- main data pull -------- */
$sql="
SELECT 
  p.ApplicationNumber,
  a.Amount,

  r.id        AS rec_ID,
  r.Stage     AS rec_Stage,
  r.Status    AS rec_Status,
  r.StatusDate AS rec_StatusDate,
  r.UserName  AS rec_UserName,
  r.Note      AS rec_Note,

  p.UserName  AS cur_UserName,
  COALESCE(u.name,p.UserName) AS cur_Name,
  p.Status    AS cur_Status,
  p.StatusDate AS cur_StatusDate

FROM pending p
LEFT JOIN Users u ON u.username=p.UserName
LEFT JOIN (
   SELECT t.*
   FROM Tracking t
   INNER JOIN (
      SELECT ApplicationNumber,MAX(id) max_id
      FROM Tracking
      WHERE Status IN ('SalesReceived','AnalystReceived')
      GROUP BY ApplicationNumber
   ) x ON x.max_id=t.id
) r ON r.ApplicationNumber=p.ApplicationNumber
LEFT JOIN (
   SELECT ta1.ApplicationNumber, ta1.Amount
   FROM TodayFileAppsAmounts ta1
   INNER JOIN (
       SELECT ApplicationNumber, MAX(id) max_id
       FROM TodayFileAppsAmounts
       GROUP BY ApplicationNumber
   ) ta2 ON ta2.max_id = ta1.id
) a ON a.ApplicationNumber = p.ApplicationNumber
WHERE DATE(p.pendingdate)=?
ORDER BY p.ApplicationNumber";
$stmt=$con->prepare($sql);
$stmt->bind_param('s',$pendingdate);
$stmt->execute();
$res=$stmt->get_result();
$count=$res->num_rows;
?>
<div class="table-responsive" style="margin:0 auto">
<table id="dataTable">
<caption style="caption-side:top;text-align:left">
  <div class="input-container left">
    <p><input type="button" class="btn btn-primary1" onclick="tableToExcel('dataTable','Data')" value="Export to Excel"></p>
    <p style="font-weight:bold"><?php echo $count;?> Applications</p>
  </div>
  <div class="input-container right">
    <input type="text" id="search" onkeyup="searchTable()" placeholder="Search..." size="10">
  </div>
</caption>

<tr>
  <th>#</th>
  <th>App&nbsp;No.</th>
  <th>Amount</th>
  <!-- received (6) -->
  <th class="receivedhead">Received&nbsp;ID</th>
  <th class="receivedhead">Received&nbsp;Stage</th>
  <th class="receivedhead">Received&nbsp;Status</th>
  <th class="receivedhead">Received&nbsp;Status&nbsp;Date</th>
  <th class="receivedhead">Received&nbsp;User&nbsp;Name</th>
  <th class="receivedhead">Received&nbsp;Note</th>
  <!-- current (4) -->
  <th class="currenthead">Current&nbsp;User&nbsp;Name</th>
  <th class="currenthead">Current&nbsp;Name</th>
  <th class="currenthead">Current&nbsp;Status</th>
  <th class="currenthead">Current&nbsp;Status&nbsp;Date</th>
  <th class="Extra1">Extra1</th>
  <th class="Extra2">Extra2</th>
  <th class="Extra3">Extra3</th>
</tr>

<?php
$i=1;
while($row=$res->fetch_assoc()){
  echo "<tr>";
  echo "<td>".$i."</td>";
  echo "<td>".$row['ApplicationNumber']."</td>";
  echo "<td>".($row['Amount']??'')."</td>";

  /* received block */
  echo "<td class='received'>".($row['rec_ID']??'')."</td>";
  echo "<td class='received'>".($row['rec_Stage']??'')."</td>";
  echo "<td class='received'>".($row['rec_Status']??'')."</td>";
  echo "<td class='received'>".($row['rec_StatusDate']??'')."</td>";
  echo "<td class='received'>".($row['rec_UserName']??'')."</td>";
  echo "<td class='received'>".($row['rec_Note']??'')."</td>";

  /* current block */
  echo "<td class='current'>".$row['cur_UserName']."</td>";
  echo "<td class='current'>".$row['cur_Name']."</td>";
  echo "<td class='current'>".$row['cur_Status']."</td>";
  echo "<td class='current'>".$row['cur_StatusDate']."</td>";
  echo "<td class='Extra1'>EXTRA1</td>";
  echo "<td class='Extra2'>EXTRA2</td>";
  echo "<td class='Extra3'>EXTRA3</td>";

  echo "</tr>";
  $i++;
}
?>
</table>
</div>

<?php
$stmt->close();
$con->close();
?>
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>
