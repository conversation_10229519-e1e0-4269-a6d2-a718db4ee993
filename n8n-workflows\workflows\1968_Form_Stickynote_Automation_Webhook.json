{"id": "r1u4HOJu5j5sP27x", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462"}, "name": "Social Media Publisher", "tags": [], "nodes": [{"id": "f5050652-6170-41c6-b3c3-0f6616c9d575", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-380, 120], "webhookId": "bb578d47-feaa-4973-96df-659089838de5", "parameters": {"options": {}, "formTitle": "Post Publisher", "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Platform", "fieldOptions": {"values": [{"option": "instagram"}, {"option": "linkedin"}, {"option": "facebook"}, {"option": "x"}, {"option": "tiktok"}, {"option": "threads"}]}, "requiredField": true}, {"fieldLabel": "Account", "placeholder": "User Profile name set on Upload-post.com", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Caption", "requiredField": true}, {"fieldType": "file", "fieldLabel": "Upload", "multipleFiles": false, "requiredField": true, "acceptFileTypes": ".jpg,.mp4"}, {"fieldLabel": "Facebook Id", "placeholder": "Facebook page Id (eg. **************)"}]}}, "typeVersion": 2.2}, {"id": "e9d8b8c9-3e40-420d-98c3-82c92f1c53c7", "name": "Post photo", "type": "n8n-nodes-base.httpRequest", "position": [220, -180], "parameters": {"url": "https://api.upload-post.com/api/upload_photos", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.Caption }}"}, {"name": "user", "value": "={{ $json.Account }}"}, {"name": "platform[]", "value": "={{ $json.Platform }}"}, {"name": "photos[]", "parameterType": "formBinaryData", "inputDataFieldName": "=Upload"}, {"name": "facebook_id", "value": "={{ $('On form submission').item.json['Facebook Id'] }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "DEE2XGvhGodgbAJh", "name": "Upload-post.com API"}}, "typeVersion": 4.2}, {"id": "04103be2-a1d8-4b00-942b-44e13dffa666", "name": "Post video", "type": "n8n-nodes-base.httpRequest", "position": [220, 280], "parameters": {"url": "https://api.upload-post.com/api/upload", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.Caption }}"}, {"name": "user", "value": "={{ $json.Account }}"}, {"name": "platform[]", "value": "={{ $json.Platform }}"}, {"name": "video", "parameterType": "formBinaryData", "inputDataFieldName": "=Upload"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "DEE2XGvhGodgbAJh", "name": "Upload-post.com API"}}, "typeVersion": 4.2}, {"id": "e7803375-20b0-4e12-80a4-b102ba48b6ba", "name": "KO Video", "type": "n8n-nodes-base.form", "position": [880, 380], "webhookId": "d03e4286-f252-4a35-a65e-ea8ac96222e4", "parameters": {"options": {}, "operation": "completion", "completionTitle": "=Oops", "completionMessage": "There was an error"}, "typeVersion": 1}, {"id": "91f2b501-4021-4ecc-9cc3-4e804d020135", "name": "OK Video", "type": "n8n-nodes-base.form", "position": [880, 180], "webhookId": "d03e4286-f252-4a35-a65e-ea8ac96222e4", "parameters": {"options": {}, "operation": "completion", "completionTitle": "=Congratulations!", "completionMessage": "Your post has been published correctly"}, "typeVersion": 1}, {"id": "b3eead97-f995-42dc-b937-6d05f284f601", "name": "KO Photo", "type": "n8n-nodes-base.form", "position": [880, -80], "webhookId": "d03e4286-f252-4a35-a65e-ea8ac96222e4", "parameters": {"options": {}, "operation": "completion", "completionTitle": "=Oops", "completionMessage": "There was an error"}, "typeVersion": 1}, {"id": "8ead4dfa-cb9e-4ab4-b52c-7a681876269f", "name": "OK Photo", "type": "n8n-nodes-base.form", "position": [880, -280], "webhookId": "d03e4286-f252-4a35-a65e-ea8ac96222e4", "parameters": {"options": {}, "operation": "completion", "completionTitle": "=Congratulations!", "completionMessage": "Your post has been published correctly"}, "typeVersion": 1}, {"id": "a5fba676-4e7b-42e2-af36-e9d63458870b", "name": "Success Photo?", "type": "n8n-nodes-base.if", "position": [640, -180], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "47eee8ce-0237-48f2-b67f-04021d3acda2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.result }}\n", "rightValue": "true"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "310853de-ab7e-481e-8efa-5a681f08032b", "name": "Success Video?", "type": "n8n-nodes-base.if", "position": [660, 280], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "47eee8ce-0237-48f2-b67f-04021d3acda2", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.result }}\n", "rightValue": "true"}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "81ec87fc-9298-4cd6-8a46-45ee6cb050b3", "name": "Result Photo", "type": "n8n-nodes-base.set", "position": [440, -180], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "551c5179-055a-415e-bf17-61f2a5dd2769", "name": "=result", "type": "string", "value": "={{ $json.results[$('Video or Photo?').item.json.Platform].success.toBoolean() }}"}]}}, "typeVersion": 3.4}, {"id": "936ac765-0e7c-411d-b70d-bdfbe8e180a6", "name": "Result Video", "type": "n8n-nodes-base.set", "position": [440, 280], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "551c5179-055a-415e-bf17-61f2a5dd2769", "name": "=result", "type": "string", "value": "={{ $json.results[$('Video or Photo?').item.json.Platform].success.toBoolean() }}"}]}}, "typeVersion": 3.4}, {"id": "3a3d66ae-7ecd-43aa-a905-10afdab753b3", "name": "Video or Photo?", "type": "n8n-nodes-base.switch", "position": [-120, 120], "parameters": {"rules": {"values": [{"outputKey": "Image", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "879af56a-8979-4d9d-9acd-6009d821f6a7", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.Upload.mimetype }}", "rightValue": "image/jpeg"}]}, "renameOutput": true}, {"outputKey": "Video", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d7ee147d-d775-4b81-b043-0873c535b400", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Upload.mimetype }}", "rightValue": "video/mp4"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "76d1cc06-8bb5-43cd-8dcc-2da1656b0335", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-400, -1020], "parameters": {"width": 1380, "height": 680, "content": "## SETTINGS\n\n- Find your API key in your [Upload-Post Manage Api Keys](https://app.upload-post.com/) 10 FREE uploads per month\n- Set the the \"Auth Header\":\n-- Name: Authorization\n-- Value: Apikey YOUR_API_KEY_HERE\n- Create profiles to manage your social media accounts. The \"Profile\" you choose will be used in the field \"Account\" on form submission (eg. test1 or test2).  \n\n![image](https://i.postimg.cc/N0BHdcQ4/uploadpost.png)\n\nApr-2025: YouTube integration is currently being verified by Google, and may not work as expected.\n"}, "typeVersion": 1}, {"id": "e9b40e23-cde9-459d-b490-8adda6b9253f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-400, -1240], "parameters": {"color": 3, "width": 1380, "height": 180, "content": "## Upload video and photo on TikTok, X, Facebook, Youtube, Instagram, Threads and Linkedin\n\nSimplify your social media workflow with our powerful platform designed for content creators and marketers.\n\nUpload your video once and let Upload-Post distribute it across all your connected social media accounts effortlessly."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ac29ff60-2d87-4646-ae63-ab543f3849de", "connections": {"Post photo": {"main": [[{"node": "Result Photo", "type": "main", "index": 0}]]}, "Post video": {"main": [[{"node": "Result Video", "type": "main", "index": 0}]]}, "Result Photo": {"main": [[{"node": "Success Photo?", "type": "main", "index": 0}]]}, "Result Video": {"main": [[{"node": "Success Video?", "type": "main", "index": 0}]]}, "Success Photo?": {"main": [[{"node": "OK Photo", "type": "main", "index": 0}], [{"node": "KO Photo", "type": "main", "index": 0}]]}, "Success Video?": {"main": [[{"node": "OK Video", "type": "main", "index": 0}], [{"node": "KO Video", "type": "main", "index": 0}]]}, "Video or Photo?": {"main": [[{"node": "Post photo", "type": "main", "index": 0}], [{"node": "Post video", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Video or Photo?", "type": "main", "index": 0}]]}}}