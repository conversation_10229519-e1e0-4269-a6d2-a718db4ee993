{"id": "ynTqojfUnGpG2rBP", "meta": {"instanceId": "bd0e051174def82b88b5cd547222662900558d74b239c4048ea0f6b7ed61c642"}, "name": "Merge multiple runs into one", "tags": [], "nodes": [{"id": "a42e0906-2d44-4b9b-b4fa-63ab3c2a6abf", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [120, 340], "parameters": {}, "typeVersion": 1}, {"id": "220df874-90fd-4cb0-aea5-f238d33a7bcc", "name": "Customer Datastore", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [340, 340], "parameters": {"operation": "getAllPeople"}, "typeVersion": 1}, {"id": "e2819ff4-9ba8-4af4-8249-1edc018493ff", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [780, 340], "webhookId": "bfa744d6-ed39-4788-a6b5-836600f368bc", "parameters": {"unit": "seconds"}, "typeVersion": 1}, {"id": "e4c50762-d7f0-420b-8043-44060cd51451", "name": "Done looping?", "type": "n8n-nodes-base.if", "position": [1220, 340], "parameters": {"conditions": {"boolean": [{"value1": "={{$node[\"Loop Over Items\"].context[\"noItemsLeft\"]}}", "value2": true}]}}, "typeVersion": 1}, {"id": "9e506657-6788-40f1-9fa0-55bd9db77ecc", "name": "Merge loop items", "type": "n8n-nodes-base.code", "position": [1440, 340], "parameters": {"jsCode": "let results = [],\n  i = 0;\n\ndo {\n  try {\n    results = results.concat($(\"NoOp\").all(0, i));\n  } catch (error) {\n    return results;\n  }\n  i++;\n} while (true);\n"}, "typeVersion": 1}, {"id": "1b6dcb04-5945-48fb-925e-370ee1154df7", "name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1000, 340], "parameters": {}, "typeVersion": 1}, {"id": "28809ed2-1465-4a12-b11b-fe1498b7e045", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [600, 340], "parameters": {"options": {}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0fd71e8c-7938-43a3-acec-fe746a183f9c", "connections": {"NoOp": {"main": [[{"node": "Done looping?", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "NoOp", "type": "main", "index": 0}]]}, "Done looping?": {"main": [[{"node": "Merge loop items", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Wait", "type": "main", "index": 0}]]}, "Customer Datastore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Customer Datastore", "type": "main", "index": 0}]]}}}