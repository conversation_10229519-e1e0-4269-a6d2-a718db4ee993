{"id": "kjyWJWfDlyXkKL3m", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "✨🔪 Advanced AI Powered Document Parsing & Text Extraction with Llama Parse", "tags": [], "nodes": [{"id": "ea7670da-896e-4b9c-b0c2-b3a3dbb6f88f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-2320, 80], "webhookId": "a9668054-5bd3-427d-8f18-932436441e42", "parameters": {"path": "parse", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "2c445d40-5d8b-469e-811e-7423f57ba054", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [-2040, -1260], "webhookId": "344de9dc-4062-4552-ae29-1e9150322cdb", "parameters": {"limit": 28, "filters": {"q": "has:attachment", "sender": " <EMAIL>"}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "f321e1d3-24ba-4623-bb31-93c7f6389aa9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-2360, -1260], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}, {}]}}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 1.2}, {"id": "ad2701f8-be77-465e-bd58-0e964ba412c0", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [-1840, -1260], "parameters": {"keep": "lastItems"}, "typeVersion": 1}, {"id": "c305dbce-714a-420e-8dd0-f5c6e80afa01", "name": "Get Message", "type": "n8n-nodes-base.gmail", "position": [-1640, -1260], "webhookId": "********-6e5b-47c1-84a4-a92cbc33b37f", "parameters": {"simple": false, "options": {"downloadAttachments": true, "dataPropertyAttachmentsPrefixName": "=file"}, "messageId": "={{ $('Gmail').item.json.id }}", "operation": "get"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "0e67527b-c886-41a1-b66b-c965fd6b44f3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-920, -1500], "parameters": {"color": 6, "width": 320, "height": 340, "content": "## Send to LlamaParse\nhttps://docs.cloud.llamaindex.ai/API/upload-file-api-v-1-parsing-upload-post"}, "typeVersion": 1}, {"id": "85e72267-7be0-49ac-b305-4c07356ce244", "name": "Parse Document with LlamaParse", "type": "n8n-nodes-base.httpRequest", "position": [-800, -1360], "parameters": {"url": "https://api.cloud.llamaindex.ai/api/parsing/upload", "method": "POST", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "contentType": "multipart-form-data", "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "=file", "parameterType": "formBinaryData", "inputDataFieldName": "file0"}, {"name": "webhook_url", "value": "=https://[YOUR-N8N-URL]/webhook/parse"}, {"name": "accurate_mode", "value": "true"}, {"name": "premium_mode", "value": "false"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "9trkgqZBCEmSt6ng", "name": "GET Webhook"}}, "executeOnce": true, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "2664705a-31d5-439b-b1e4-fc6b708a7baa", "name": "Summarize Email", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-820, -680], "parameters": {"text": "={{ $('Is there an Email Attachement').item.json.text }}", "messages": {"messageValues": [{"message": "You are an expert at summarizing email messages.  Provide a summary of the provided email."}]}, "promptType": "define"}, "typeVersion": 1.5}, {"id": "1405f933-b281-469f-a5b7-0de2f820dd09", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-720, -540], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "ea299469-7889-45c9-a8f1-679be09e5aaf", "name": "Save LlamaParse ID and Summary to Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [-140, -1020], "parameters": {"columns": {"value": {"jobid": "={{ $json.id }}", "summary": "={{ $json.text }}", "image_url": "={{ $json.webViewLink }}"}, "schema": [{"id": "jobid", "type": "string", "display": true, "removed": false, "required": false, "displayName": "jobid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "statement_date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "statement_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "org_name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "org_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "member_name", "type": "string", "display": true, "removed": true, "required": false, "displayName": "member_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "subtotal", "type": "string", "display": true, "removed": true, "required": false, "displayName": "subtotal", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "service_fees_total", "type": "string", "display": true, "removed": true, "required": false, "displayName": "service_fees_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tips_total", "type": "string", "display": true, "removed": true, "required": false, "displayName": "tips_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_excl_gst", "type": "string", "display": true, "removed": true, "required": false, "displayName": "current_excl_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "container_deposit_total", "type": "string", "display": true, "removed": true, "required": false, "displayName": "container_deposit_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "outstanding_gst", "type": "string", "display": true, "removed": true, "required": false, "displayName": "outstanding_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "paid_gst", "type": "string", "display": true, "removed": true, "required": false, "displayName": "paid_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_gst", "type": "string", "display": true, "removed": true, "required": false, "displayName": "total_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_bc_pst", "type": "string", "display": true, "removed": true, "required": false, "displayName": "total_bc_pst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_bc_pst_liquor", "type": "string", "display": true, "removed": true, "required": false, "displayName": "total_bc_pst_liquor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_savings", "type": "string", "display": true, "removed": true, "required": false, "displayName": "total_savings", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "final_amount_due", "type": "string", "display": true, "removed": true, "required": false, "displayName": "final_amount_due", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "payment_reference", "type": "string", "display": true, "removed": true, "required": false, "displayName": "payment_reference", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "payment_amount", "type": "string", "display": true, "removed": true, "required": false, "displayName": "payment_amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "transaction_number", "type": "string", "display": true, "removed": true, "required": false, "displayName": "transaction_number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "image_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["jobid"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo/edit#gid=0", "cachedResultName": "Expenses"}, "documentId": {"__rl": true, "mode": "list", "value": "1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo/edit?usp=drivesdk", "cachedResultName": "2024.Year.End.Expenses"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "SOLbth24hZWisXAv", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "d8454cf2-5bef-4bfa-9471-c358ff067765", "name": "Save Document to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-820, -1020], "parameters": {"name": "={{ $('Is there an Email Attachement').item.json.id }}_{{ $('Is there an Email Attachement').item.binary.file0.fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "inputDataFieldName": "=file0"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "03f46b72-9e18-44a4-85ef-0eea058c3c6d", "name": "Extract Invoice Details as JSON", "type": "@n8n/n8n-nodes-langchain.chainLlm", "onError": "continueErrorOutput", "position": [-1180, 500], "parameters": {"text": "=Analyze this markdown content and convert it to JSON following this exact schema:\n{\n    \"invoice_details\": {\n        \"statement_date\": \"\",\n        \"organization\": {\n            \"name\": \"\",\n            \"address\": \"\",\n            \"gst_number\": \"\"\n        },\n        \"member\": {\n            \"name\": \"\",\n            \"company\": \"\",\n            \"address\": \"\",\n            \"contact_number\": \"\",\n            \"organization_number\": \"\"\n        }\n    },\n    \"transactions\": [\n        {\n            \"date\": \"\",\n            \"document_number\": \"\",\n            \"description\": \"\",\n            \"base_amount\": 0.00,\n            \"gst\": 0.00,\n            \"bc_pst\": 0.00,\n            \"bc_pst_liquor\": 0.00,  # Added for liquor PST\n            \"container_deposit\": 0.00,  # Added for bottle deposits\n            \"service_fee\": 0.00,\n            \"tip_amount\": 0.00,\n            \"regular_price\": 0.00,  # Added for regular price tracking\n            \"savings_amount\": 0.00,  # Added for savings tracking\n            \"total_charge\": 0.00,\n            \"transaction_type\": \"\"\n        }\n    ],\n    \"payment_details\": {\n        \"previous_balance\": 0.00,\n        \"payment_amount\": 0.00,\n        \"payment_reference\": \"\",\n        \"payment_date\": \"\",\n        \"payment_method\": \"\",\n        \"payment_status\": \"\",\n        \"card_number\": \"\",  # Added for card details\n        \"auth_number\": \"\",  # Added for authorization\n        \"transaction_number\": \"\"\n    },\n    \"invoice_summary\": {\n        \"subtotal\": 0.00,\n        \"service_fees_total\": 0.00,\n        \"tips_total\": 0.00,\n        \"current_excl_gst\": 0.00,\n        \"container_deposit_total\": 0.00,  # Added for deposits\n        \"outstanding_gst\": 0.00,\n        \"paid_gst\": 0.00,\n        \"total_gst\": 0.00,\n        \"total_bc_pst\": 0.00,\n        \"total_bc_pst_liquor\": 0.00,  # Added for liquor PST if shown in markdown content\n        \"total_savings\": 0.00,  # Added for savings\n        \"final_amount_due\": 0.00\n    },\n    \"payment_terms\": {\n        \"due_date\": \"\",\n        \"processing_date\": \"\",\n        \"special_notices\": [],\n        \"cancellation_policy\": \"\",\n        \"refund_policy\": \"\",\n        \"return_policy\": \"\"  # Added for return policy\n    },\n    \"additional_info\": {\n        \"booking_number\": \"\",\n        \"transaction_time\": \"\",  # Added for transaction time\n        \"register_info\": \"\",     # Added for register details\n        \"event_details\": {\n            \"date\": \"\",\n            \"time\": \"\",\n            \"location\": \"\"\n        },\n        \"special_instructions\": []\n    },\n    \"summary\": \"\" # The natural language summary of the invoice\n}\n\nMarkdown Content:\n{{ $json.data }}\n\nImportant:\n- Extract exact values from the markdown\n- Return only valid JSON\n- Include all fields even if empty\n- Format numbers as floats with 2 decimal places\n- Track container deposits separately\n- Show liquor PST (10%) separately from regular PST if provided in markdown content\n- Include regular prices and savings amounts\n- Track transaction details including card info and authorization\n- Parse return policy information\n- Include register and transaction time details\n- Ensure final_amount_due equals the sum of all applicable charges and taxes\n- Summarize the markdown contents\n- Only output valid JSON without any preamble or further explanation.  Remove any ```json and ``` from response.", "promptType": "define"}, "typeVersion": 1.5, "alwaysOutputData": true}, {"id": "3c371677-76e8-45d7-8c05-a4ca1cc0b1fe", "name": "gpt-4o-mini1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1600, 240], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "9ff3f86f-9ffc-42fa-b428-a6bfabf2426a", "name": "gpt-4o-mini2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1080, 640], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "69a0505f-1fe4-4581-ad2d-5bc7d68874e9", "name": "Update Google Sheet by LlamaParse ID", "type": "n8n-nodes-base.googleSheets", "position": [-540, 600], "parameters": {"columns": {"value": {"jobid": "={{ $('Webhook').item.json.body.jobId }}", "summary": "={{ $json.output.summary }}", "org_name": "={{ $json.output.invoice_details.organization.name }}", "paid_gst": "={{ $json.output.invoice_summary.paid_gst }}", "subtotal": "={{ $json.output.invoice_summary.subtotal }}", "total_gst": "={{ $json.output.invoice_summary.total_gst }}", "tips_total": "={{ $json.output.invoice_summary.tips_total }}", "member_name": "={{ $json.output.invoice_details.member.name }}", "total_bc_pst": "={{ $json.output.invoice_summary.total_bc_pst }}", "total_savings": "={{ $json.output.invoice_summary.total_savings }}", "payment_amount": "={{ $json.output.payment_details.payment_amount }}", "statement_date": "={{ $json.output.invoice_details.statement_date }}", "outstanding_gst": "={{ $json.output.invoice_summary.outstanding_gst }}", "current_excl_gst": "={{ $json.output.invoice_summary.current_excl_gst }}", "final_amount_due": "={{ $json.output.invoice_summary.final_amount_due }}", "payment_reference": "={{ $json.output.payment_details.payment_reference }}", "service_fees_total": "={{ $json.output.invoice_summary.service_fees_total }}", "transaction_number": "={{ $json.output.payment_details.transaction_number }}", "total_bc_pst_liquor": "={{ $json.output.invoice_summary.total_bc_pst_liquor }}", "container_deposit_total": "={{ $json.output.invoice_summary.container_deposit_total }}"}, "schema": [{"id": "jobid", "type": "string", "display": true, "removed": false, "required": false, "displayName": "jobid", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "statement_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "statement_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "org_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "org_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "member_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "member_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "subtotal", "type": "string", "display": true, "removed": false, "required": false, "displayName": "subtotal", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "service_fees_total", "type": "string", "display": true, "removed": false, "required": false, "displayName": "service_fees_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tips_total", "type": "string", "display": true, "removed": false, "required": false, "displayName": "tips_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "current_excl_gst", "type": "string", "display": true, "removed": false, "required": false, "displayName": "current_excl_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "container_deposit_total", "type": "string", "display": true, "removed": false, "required": false, "displayName": "container_deposit_total", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "outstanding_gst", "type": "string", "display": true, "removed": false, "required": false, "displayName": "outstanding_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "paid_gst", "type": "string", "display": true, "removed": false, "required": false, "displayName": "paid_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_gst", "type": "string", "display": true, "removed": false, "required": false, "displayName": "total_gst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_bc_pst", "type": "string", "display": true, "removed": false, "required": false, "displayName": "total_bc_pst", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_bc_pst_liquor", "type": "string", "display": true, "removed": false, "required": false, "displayName": "total_bc_pst_liquor", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "total_savings", "type": "string", "display": true, "removed": false, "required": false, "displayName": "total_savings", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "final_amount_due", "type": "string", "display": true, "removed": false, "required": false, "displayName": "final_amount_due", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "payment_reference", "type": "string", "display": true, "removed": false, "required": false, "displayName": "payment_reference", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "payment_amount", "type": "string", "display": true, "removed": false, "required": false, "displayName": "payment_amount", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "transaction_number", "type": "string", "display": true, "removed": false, "required": false, "displayName": "transaction_number", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["jobid"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo/edit#gid=0", "cachedResultName": "Expenses"}, "documentId": {"__rl": true, "mode": "list", "value": "1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1mUvDI9sGvRl64iNV6ODXUzro5Q3Oeuaks5662tfN7Oo/edit?usp=drivesdk", "cachedResultName": "2024.Year.End.Expenses"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "SOLbth24hZWisXAv", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "19907cba-4530-4f25-8a6f-435b1f8d23ad", "name": "Invoice Details", "type": "n8n-nodes-base.set", "position": [-780, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e145ed8c-cdea-4e5a-ba11-d8ce595dfb8d", "name": "output", "type": "object", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "af95c024-8e36-499b-af32-4c661da49a61", "name": "Prepare Message", "type": "n8n-nodes-base.set", "position": [-540, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3e566101-2ad9-444b-8459-451ba6a91575", "name": "invoice_details.statement_date", "type": "string", "value": "={{ $json.output.invoice_details.statement_date }}"}, {"id": "57a466f6-d354-4864-98d3-ba4673afde98", "name": "invoice_details.organization", "type": "object", "value": "={{ $json.output.invoice_details.organization }}"}, {"id": "e1b22978-8114-4956-a5fc-3efbc43335a3", "name": "invoice_details.member", "type": "object", "value": "={{ $json.output.invoice_details.member }}"}, {"id": "e45a744c-0874-48b7-b59a-9d83aad27ff3", "name": "payment_details", "type": "object", "value": "={{ $json.output.payment_details }}"}, {"id": "c0335dc7-1b5c-41fc-b60a-bf45248c9f7f", "name": "invoice_summary", "type": "object", "value": "={{ $json.output.invoice_summary }}"}, {"id": "6c9ba3bf-37a6-4a8f-b97d-991f3ce6950f", "name": "summary", "type": "string", "value": "={{ $json.output.summary }}"}]}}, "typeVersion": 3.4}, {"id": "333f42a7-2665-4613-89c9-c184d764af37", "name": "Send Invoice Details as Telegram Message", "type": "n8n-nodes-base.telegram", "position": [-340, 400], "webhookId": "04464e72-2be3-4df9-8a08-18d23cb75d72", "parameters": {"text": "={{ $json.summary }}\n--------\n{{ $json.invoice_summary.toJsonString() }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "faa0768f-1d4c-42c4-902c-b2d0d40f0eb4", "name": "gpt-4o-mini3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1080, 60], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "d1a54284-60d1-4fac-b81b-4ed1610ddf2e", "name": "Send Error Message 2", "type": "n8n-nodes-base.telegram", "position": [-780, 600], "webhookId": "3ba1ee6d-1648-4421-823b-e68ae14d769b", "parameters": {"text": "=Error in workflow", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "b1b50042-8270-4e13-b7b1-6d017e9be8d9", "name": "Send Error Message 1", "type": "n8n-nodes-base.telegram", "position": [-780, 60], "webhookId": "3ba1ee6d-1648-4421-823b-e68ae14d769b", "parameters": {"text": "=Error in workflow", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "a365c8a1-c0fb-43f7-84fa-b68a0e9c087e", "name": "Send Document Summary as Telegram Message", "type": "n8n-nodes-base.telegram", "position": [-540, -200], "webhookId": "04464e72-2be3-4df9-8a08-18d23cb75d72", "parameters": {"text": "={{ $json.text }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "6abd00a0-2971-49f9-812f-f65a0004136b", "name": "Summarize Document", "type": "@n8n/n8n-nodes-langchain.chainLlm", "onError": "continueErrorOutput", "position": [-1180, -80], "parameters": {"text": "=Please analyze this document and provide:\n\n## Document Analysis\n- A concise executive summary (2-3 sentences)\n- Key themes and main points\n- Notable findings or insights\n- Data highlights and important statistics (if applicable)\n\n## Recommendations\n- Action items or next steps\n- Areas requiring further investigation\n- Potential implications\n\n## Format Requirements\n- Structure the analysis using clear headers and sections\n- Include relevant quotes to support key points\n- Present any numerical data in tables or bullet points\n- Highlight critical information using bold text\n\nPlease maintain the original document's context while making the content more accessible and actionable.\n\nHere is the document: {{ $json.data }}\n", "promptType": "define"}, "typeVersion": 1.5, "alwaysOutputData": true}, {"id": "e672bcf3-0d5f-4410-ac5b-660c3ba0c456", "name": "Classify Parsed Document", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-1680, 80], "parameters": {"options": {}, "inputText": "={{ $json.data }}", "categories": {"categories": [{"category": "not invoice", "description": "The document is not an invoice"}, {"category": "invoice", "description": "The document is an invoice"}]}}, "typeVersion": 1}, {"id": "cc522966-3e6a-4830-bde9-d4e251752ec0", "name": "Get Parsed Markdown", "type": "n8n-nodes-base.set", "position": [-1980, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "55b5a755-eeaf-4ce7-b600-e6c864dc7e10", "name": "data", "type": "string", "value": "={{ $json.body.md }}"}]}}, "typeVersion": 3.4}, {"id": "683fa521-dfd0-4b1c-905f-d5a4f56ab65a", "name": "Prepare Data", "type": "n8n-nodes-base.set", "position": [-640, -1020], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cee9e2d3-b311-4903-9867-e7d3d7ed2456", "name": "google_drive_fileid", "type": "string", "value": "={{ $json.id }}"}, {"id": "5c6eddf6-5a5e-4c51-87ed-8e3aabc2f65d", "name": "webViewLink", "type": "string", "value": "={{ $json.webViewLink }}"}]}}, "typeVersion": 3.4}, {"id": "b64a21ab-0e1f-4d6c-b718-a9aaaa27ae19", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [-2040, -860], "parameters": {"url": "https://api.cloud.llamaindex.ai/api/parsing/supported_file_extensions", "options": {}}, "typeVersion": 4.2}, {"id": "cd0699cf-3a95-4dc8-806a-6a01339c598d", "name": "Is there an Email Attachement", "type": "n8n-nodes-base.if", "position": [-1420, -1260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "460b82e5-30f5-4cb3-a937-a275fd256fcc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $input.item.binary }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "269ba37f-fa18-4333-be3c-eee6ef5c0f56", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [-1840, -860], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "extensions"}, "typeVersion": 1}, {"id": "dffd2e83-58ff-49a0-b547-3b6f4b92dfa9", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-1620, -860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c9c59aae-b507-4493-a047-495bed344a5e", "name": "extension", "type": "string", "value": "=.{{ $('Is there an Email Attachement').item.binary.file0.fileExtension }}"}]}}, "typeVersion": 3.4}, {"id": "02a121a4-edea-45c4-b325-2f61b3d0b02e", "name": "Merge Email Processing", "type": "n8n-nodes-base.merge", "position": [-380, -1020], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition", "numberInputs": 3}, "typeVersion": 3}, {"id": "c1310be3-6448-48d1-a954-caa3d4651075", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [-1120, -1020], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "dbe3a235-0bae-4743-b53e-154b75911482", "name": "If Supported File Extensions", "type": "n8n-nodes-base.if", "position": [-1420, -860], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f76cc5a7-6882-4e1f-86d5-99d5d9e90a34", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('Aggregate').item.json.extensions.includes($json.extension)}}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "1413f84b-d1a9-4b0c-ae43-7f303a54527e", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [-1120, -1260], "parameters": {}, "typeVersion": 1}, {"id": "9f3ae287-cb8b-466c-8dbe-678be30c2c04", "name": "No Operation, do nothing1", "type": "n8n-nodes-base.noOp", "position": [-1120, -780], "parameters": {}, "typeVersion": 1}, {"id": "6f9b5ae2-22e8-4dc8-ba0b-06fbc585f209", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2140, -980], "parameters": {"width": 920, "height": 320, "content": "## Check for Supported File Extension\nhttps://docs.cloud.llamaindex.ai/API/get-supported-file-extensions-api-v-1-parsing-supported-file-extensions-get"}, "typeVersion": 1}, {"id": "28c5c09a-9a15-4af9-8253-59ae36dfe390", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-2140, -1420], "parameters": {"color": 3, "width": 920, "height": 400, "content": "## Get Emails with Attachments\n### ☀️Disclaimer\nThis workflow only processes the the first attachment of the email.\nAdjust search and limit settings to suit your use case."}, "typeVersion": 1}, {"id": "3174a934-3b64-47b2-b81b-bfe717a034e2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-920, -1120], "parameters": {"color": 4, "width": 460, "height": 300, "content": "## Save Document to Google Drive"}, "typeVersion": 1}, {"id": "92f079d1-c5bd-45fe-9372-7ff521eda15b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-920, -780], "parameters": {"color": 5, "width": 460, "height": 380, "content": "## Summarize the Email Message"}, "typeVersion": 1}, {"id": "fd7d7e7a-005a-4a43-a3de-e9bb036bb615", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-220, -1120], "parameters": {"color": 4, "width": 300, "height": 300, "content": "## Save To Google Sheets"}, "typeVersion": 1}, {"id": "c6469054-0345-4371-8928-21a04c21b131", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-3060, -1540], "parameters": {"width": 540, "height": 1340, "content": "# Description\n\nThis workflow automates document processing using LlamaParse to extract and analyze text from various file formats. It intelligently processes documents, extracts structured data, and delivers actionable insights through multiple channels.\n\n## How It Works\n\n### Document Ingestion & Processing 📄\n- Monitors Gmail for incoming attachments or accepts documents via webhook\n- Validates file formats against supported LlamaParse extensions\n- Uploads documents to LlamaParse for advanced text extraction\n- Stores original documents in Google Drive for reference\n\n### Intelligent Document Analysis 🧠\n- Automatically classifies document types (invoices, reports, etc.)\n- Extracts structured data using customized AI prompts\n- Generates comprehensive document summaries with key insights\n- Converts unstructured text into organized JSON data\n\n### Invoice Processing Automation 💼\n- Extracts critical invoice details (dates, amounts, line items)\n- Organizes financial data into structured formats\n- Calculates tax breakdowns, subtotals, and payment information\n- Maintains detailed records for accounting purposes\n\n### Multi-Channel Delivery 📱\n- Saves extracted data to Google Sheets for tracking and analysis\n- Sends concise summaries via Telegram for immediate review\n- Creates searchable document archives in Google Drive\n- Updates spreadsheets with structured financial information\n\n## Setup Steps\n\n### Configure API Credentials 🔑\n- Set up LlamaParse API connection\n- Configure Gmail OAuth for email monitoring\n- Set up Google Drive and Sheets integrations\n- Add Telegram bot credentials for notifications\n\n### Customize AI Processing ⚙️\n- Adjust document classification parameters\n- Modify extraction templates for specific document types\n- Fine-tune summary generation prompts\n- Customize invoice data extraction schema\n\n### Test and Deploy 🚀\n- Test with sample documents of various formats\n- Verify data extraction accuracy\n- Confirm notification delivery\n- Monitor processing pipeline performance\n\n\n"}, "typeVersion": 1}, {"id": "b2024905-5c3b-49d5-89b9-ef41c4a4283c", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-2440, -1340], "parameters": {"color": 4, "width": 260, "height": 280, "content": "## 👍Try Me!"}, "typeVersion": 1}, {"id": "22284854-4005-4678-94f8-d914e031e6fc", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-2480, -1540], "parameters": {"color": 7, "width": 2600, "height": 1180, "content": "# ✨🔪 Advanced AI Powered Document Parsing & Text Extraction with Llama Parse\n"}, "typeVersion": 1}, {"id": "4f0c910e-7ae6-40ac-a659-c14a6704aaba", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1280, 280], "parameters": {"color": 6, "width": 1160, "height": 540, "content": "## Example Invoice Processing\n"}, "typeVersion": 1}, {"id": "e63bbfe8-8be7-4e3f-a8f5-a85b2ee82959", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-1220, 360], "parameters": {"width": 360, "height": 420, "content": "## Extract Invoice as JSON\n☀️Update User & System Prompt for Your Specific Use Case"}, "typeVersion": 1}, {"id": "d321e139-0828-4932-b5a9-ef11f6ae9baa", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1280, -280], "parameters": {"color": 5, "width": 980, "height": 520, "content": "## Example Document Summarizing\n"}, "typeVersion": 1}, {"id": "ab9081bd-c1c5-4db1-8dcd-ff243a7ab9be", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1220, -200], "parameters": {"width": 360, "height": 400, "content": "## Summarize Document\n☀️Update User & System Prompt for Your Specific Use Case"}, "typeVersion": 1}, {"id": "c08bbfa8-abe1-47e2-babe-b62581bcd011", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-1760, -40], "parameters": {"color": 4, "width": 440, "height": 420, "content": "## Classify Parsed Document\nAdd More Classifications as Required"}, "typeVersion": 1}, {"id": "5ffb907f-9701-401e-85e8-3b91a706ab10", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-2060, -40], "parameters": {"color": 3, "width": 260, "height": 320, "content": "## Parsed <PERSON> from LlamaP<PERSON><PERSON>"}, "typeVersion": 1}, {"id": "a53034d2-34df-421a-aa14-d9d1bbc00fc5", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-2440, -40], "parameters": {"width": 340, "height": 320, "content": "## Receive Parsed Document from LlamaParse"}, "typeVersion": 1}, {"id": "933f03f2-c231-4dcd-8aeb-ce716b8cc00e", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [-2480, -320], "parameters": {"color": 7, "width": 2400, "height": 1180, "content": "# 🪝Webhook to Receive LlamaParse Response"}, "typeVersion": 1}, {"id": "505a51e4-dea1-4876-964e-f59af728c65b", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [-1760, 420], "parameters": {"color": 5, "width": 440, "height": 400, "content": "## Save Parsed Document to Google Drive"}, "typeVersion": 1}, {"id": "33ca5eaf-30da-4360-a12b-a7dd8614743f", "name": "Save Summarized Document to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-540, 0], "parameters": {"name": "={{ $('Webhook').item.json.body.jobId }}-summary.md", "content": "={{ $json.text }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "a1e8264f-fa99-49a5-a837-6aaf3a2dc39a", "name": "Save Parsed Document to Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-1600, 560], "parameters": {"name": "={{ $('Webhook').item.json.body.jobId }}-parsed.md", "content": "={{ $json.data }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultUrl": "https://drive.google.com/drive", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}], "active": true, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "c11e3a8a-499b-4b1e-b919-ffbed36ba898", "connections": {"Gmail": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Get Message", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Parse Document with LlamaParse", "type": "main", "index": 0}, {"node": "Save Document to Google Drive", "type": "main", "index": 0}, {"node": "Summarize Email", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Get Parsed Markdown", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "If Supported File Extensions", "type": "main", "index": 0}]]}, "Get Message": {"main": [[{"node": "Is there an Email Attachement", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Summarize Email", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Prepare Data": {"main": [[{"node": "Merge Email Processing", "type": "main", "index": 1}]]}, "gpt-4o-mini1": {"ai_languageModel": [[{"node": "Classify Parsed Document", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini2": {"ai_languageModel": [[{"node": "Extract Invoice Details as JSON", "type": "ai_languageModel", "index": 0}]]}, "gpt-4o-mini3": {"ai_languageModel": [[{"node": "Summarize Document", "type": "ai_languageModel", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Invoice Details": {"main": [[{"node": "Prepare Message", "type": "main", "index": 0}, {"node": "Update Google Sheet by LlamaParse ID", "type": "main", "index": 0}]]}, "Prepare Message": {"main": [[{"node": "Send Invoice Details as Telegram Message", "type": "main", "index": 0}]]}, "Summarize Email": {"main": [[{"node": "Merge Email Processing", "type": "main", "index": 2}]]}, "Summarize Document": {"main": [[{"node": "Send Document Summary as Telegram Message", "type": "main", "index": 0}, {"node": "Save Summarized Document to Google Drive", "type": "main", "index": 0}], [{"node": "Send Error Message 1", "type": "main", "index": 0}]]}, "Get Parsed Markdown": {"main": [[{"node": "Classify Parsed Document", "type": "main", "index": 0}, {"node": "Save Parsed Document to Google Drive", "type": "main", "index": 0}]]}, "Merge Email Processing": {"main": [[{"node": "Save LlamaParse ID and Summary to Google Sheets", "type": "main", "index": 0}]]}, "Classify Parsed Document": {"main": [[{"node": "Summarize Document", "type": "main", "index": 0}], [{"node": "Extract Invoice Details as JSON", "type": "main", "index": 0}]]}, "If Supported File Extensions": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Is there an Email Attachement": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Save Document to Google Drive": {"main": [[{"node": "Prepare Data", "type": "main", "index": 0}]]}, "Parse Document with LlamaParse": {"main": [[{"node": "Merge Email Processing", "type": "main", "index": 0}]]}, "Extract Invoice Details as JSON": {"main": [[{"node": "Invoice Details", "type": "main", "index": 0}], [{"node": "Send Error Message 2", "type": "main", "index": 0}]]}, "Save LlamaParse ID and Summary to Google Sheets": {"main": [[]]}}}