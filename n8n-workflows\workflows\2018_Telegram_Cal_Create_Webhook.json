{"id": "xe9sXQUc7yW8P8im", "meta": {"instanceId": "9219ebc7795bea866f70aa3d977d54417fdf06c41944be95e20cfb60f992db19", "templateCredsSetupCompleted": true}, "name": "Meeting booked - to newsletter and CRM", "tags": [{"id": "55FGhjeaCcjBUam6", "name": "1node", "createdAt": "2025-04-30T08:13:16.484Z", "updatedAt": "2025-04-30T08:13:16.484Z"}, {"id": "0eaHel3jWsgsvzT6", "name": "template", "createdAt": "2025-04-30T08:13:16.487Z", "updatedAt": "2025-04-30T08:13:16.487Z"}, {"id": "33yuvdx4oQ05TZoD", "name": "newsletter", "createdAt": "2025-05-02T08:18:43.148Z", "updatedAt": "2025-05-02T08:18:43.148Z"}], "nodes": [{"id": "715f9c0b-58a6-46b9-b732-334cc2fb3a60", "name": "Split Attendees", "type": "n8n-nodes-base.splitOut", "position": [-460, -140], "parameters": {"options": {}, "fieldToSplitOut": "attendees"}, "typeVersion": 1}, {"id": "171ed51e-6277-46d3-9037-8b2722ca06d0", "name": "Add users", "type": "n8n-nodes-base.googleSheets", "position": [200, -140], "parameters": {"columns": {"value": {"title": "={{ $('on New Booking').item.json.eventTitle }}", "length": "={{ $('on New Booking').item.json.length }}", "timeZone": "={{ $json.timeZone }}", "createdAt": "={{ $('on New Booking').item.json.createdAt }}", "attendeeName": "={{ $json.name }}", "meetingStart": "={{ $('on New Booking').item.json.startTime }}", "attendeeEmail": "={{ $json.email }}"}, "schema": [{"id": "createdAt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "createdAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "meetingStart", "type": "string", "display": true, "removed": false, "required": false, "displayName": "meetingStart", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "attendee<PERSON><PERSON>", "type": "string", "display": true, "removed": false, "required": false, "displayName": "attendee<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "attendeeEmail", "type": "string", "display": true, "removed": false, "required": false, "displayName": "attendeeEmail", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timeZone", "type": "string", "display": true, "removed": false, "required": false, "displayName": "timeZone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "length", "type": "string", "display": true, "removed": false, "required": false, "displayName": "length", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1SJosfoM-WZEimBQTz1mu65xiyuq9bHII0Igd1mgCcq0/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1SJosfoM-WZEimBQTz1mu65xiyuq9bHII0Igd1mgCcq0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1SJosfoM-WZEimBQTz1mu65xiyuq9bHII0Igd1mgCcq0/edit?usp=drivesdk", "cachedResultName": "Calendar bookings"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "twZdLFsI3kTnqtpG", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "3b22d814-fe80-4c5b-814f-4e2666c96ca3", "name": "on New Booking", "type": "n8n-nodes-base.calTrigger", "position": [-680, -140], "webhookId": "0b5ccb99-8c0a-47e4-a970-403e607c89ed", "parameters": {"events": ["BOOKING_CREATED"], "options": {}}, "credentials": {"calApi": {"id": "3JuO2rbGXKSX0VL9", "name": "Cal account"}}, "typeVersion": 2}, {"id": "020e7fc5-7f72-434f-8a84-15b177237146", "name": "Add subscriber", "type": "n8n-nodes-base.httpRequest", "position": [420, -140], "parameters": {"url": "=https://api.beehiiv.com/v2/publications/{{ $('set data').item.json.publicationId }}/subscriptions", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $('Split Attendees').item.json.email }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('set data').item.json.beehiivAPI }}"}]}}, "typeVersion": 4.2}, {"id": "35678a8f-e844-484f-b7f3-7df5a80f4a2d", "name": "<PERSON>tendee", "type": "n8n-nodes-base.set", "position": [-20, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "70fc23eb-95b5-43ba-9067-8d834d440684", "name": "name", "type": "string", "value": "={{ $json.name }}"}, {"id": "2aa7594d-f6fd-4437-a7b0-ad3e23b0e719", "name": "email", "type": "string", "value": "={{ $json.email }}"}, {"id": "7a2f22da-04e1-4507-b135-1fdfdcdda77f", "name": "timeZone", "type": "string", "value": "={{ $json.timeZone }}"}]}}, "typeVersion": 3.4}, {"id": "678b7ca8-2ecf-44b4-a420-e40600d09a74", "name": "notify in channel", "type": "n8n-nodes-base.telegram", "position": [640, -140], "webhookId": "7ade83ec-58fa-4b43-aa3b-93bb0d9ae712", "parameters": {"text": "=📅 New meeting booked\n\nEvent name: {{ $('Add users').item.json.title }}\nStart Date: {{ $('Add users').item.json.meetingStart }} UTC\nName: {{ $('Add users').item.json.attendeeName }}\nEmail: {{ $('Add users').item.json.attendeeEmail }}\nUser time zone: {{ $('Add users').item.json.timeZone }}", "chatId": "={{ $('set data').item.json.chatID }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "IgrntTxsoDphh19z", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "1e23785f-1a3d-4d0c-a7d1-1ebc6209d5c8", "name": "set data", "type": "n8n-nodes-base.set", "position": [-240, -140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "797d5771-b242-47f5-a0c6-dc3a1a8fb15b", "name": "chatID", "type": "string", "value": "yourChatId"}, {"id": "1c3239e8-6fe2-48ad-9083-04d108a95aec", "name": "beehiivAPI", "type": "string", "value": "yourAPIkey"}, {"id": "8c94a712-93a6-48de-8a27-93c5ed0e68eb", "name": "publicationId", "type": "string", "value": "yourBeehiivPublicationId"}]}}, "typeVersion": 3.4}, {"id": "cf66eff2-ca4d-4e52-b476-26334a82275f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-330, -460], "parameters": {"width": 280, "height": 480, "content": "## Define your parameters\n- Find your telegram chat id to get notified in a private channel (bot must be added as admin)\n- Create an account on [Beehiiv]() and get your api key\n- Get your publication id from your newsletter which will be parsed in the \"Add subscriber\" url endpoint"}, "typeVersion": 1}, {"id": "cc33ebc8-d966-4c17-ab4d-8fa6dde58c37", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1160, -380], "parameters": {"width": 400, "height": 540, "content": "## How it works\nThis workflow allows you to add each meeting guest into Google Sheets, adding one row for each guest and consequently adding those users to your newsletter on Beehiiv.\n\n## Set up steps\n\n- Create an account on [Cal.com](https://refer.cal.com/1node)\n- Create a new webhook on [Cal.com](https://refer.cal.com/1node) and send a test event to the URL that appears in the first node. You will get test data that you can pin to set the rest of the workflow.\n- For [Beehiiv](https://www.beehiiv.com?via=1node-ai) you will need to get the publication id from the account plus the api key. You will find those on your account settings. Define the publication id on the \"set data\" node, together with your Telegram chat id, if you wish to notify yourself in a private channel when a new subscriber is added.\n\nEnjoy building!\n\nAitor\n[1 Node](https://1node.ai)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "qFOYM3IA9QZ7fMym", "executionOrder": "v1"}, "versionId": "64184a3c-c62a-4bb1-ae93-d7b6d22d85a2", "connections": {"set data": {"main": [[{"node": "<PERSON>tendee", "type": "main", "index": 0}]]}, "Add users": {"main": [[{"node": "Add subscriber", "type": "main", "index": 0}]]}, "Set Attendee": {"main": [[{"node": "Add users", "type": "main", "index": 0}]]}, "Add subscriber": {"main": [[{"node": "notify in channel", "type": "main", "index": 0}]]}, "on New Booking": {"main": [[{"node": "Split Attendees", "type": "main", "index": 0}]]}, "Split Attendees": {"main": [[{"node": "set data", "type": "main", "index": 0}]]}}}