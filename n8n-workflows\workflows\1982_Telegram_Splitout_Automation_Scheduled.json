{"id": "tHgDFmFyuj6DnP6l", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🎦💌Advanced YouTube RSS Feed Buddy for Your Favorite Channels", "tags": [], "nodes": [{"id": "ab1db660-96d6-402c-b6e8-8c4d278577d1", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [60, -220], "webhookId": "f6b3bbf7-b6e9-4ade-add4-12004d70b61c", "parameters": {"options": {"appendAttribution": false}, "formTitle": "RSS Feed for YouTube Channels", "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "YouTube Channel Ids", "placeholder": "[ \"UCMcoud_ZW7cfxeIugBflSBw\", \"UCtevzRsHEKhs-RK8pAqwSyQ\" ]"}]}, "responseMode": "lastNode", "formDescription": "Create RSS Feeds for Your Favorite YouTube Channels"}, "typeVersion": 2.2}, {"id": "6ea8d71f-a5f8-46d5-bf51-20df75ec6202", "name": "Create RSS Feed URLs1", "type": "n8n-nodes-base.set", "position": [60, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8159d367-513c-406b-8ad7-36f65c2e6512", "name": "rss_feed_url", "type": "string", "value": "=https://www.youtube.com/feeds/videos.xml?channel_id={{ $json.youtube_channel_id }}"}]}}, "typeVersion": 3.4}, {"id": "038e95ce-656e-4f6a-a9d8-96555aeeccf2", "name": "Get Channel Ids", "type": "n8n-nodes-base.set", "position": [460, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4b276cf0-6bb5-489e-a776-327291608b8e", "name": "ids", "type": "array", "value": "={{ $json[\"YouTube Channel Ids\"].length > 0  ? $json[\"YouTube Channel Ids\"] : $json[\"Default YouTube Channel Ids\"] }}"}]}}, "typeVersion": 3.4}, {"id": "7c8b43fb-52c9-4a3e-b5ff-2c21fe8fb183", "name": "Create YouTube API URL", "type": "n8n-nodes-base.code", "position": [1380, -220], "parameters": {"jsCode": "// Define the base URL for the YouTube Data API\nconst BASE_URL = 'https://www.googleapis.com/youtube/v3/videos';\n\n// Get all input items\nconst items = $input.all();\n\n// Process each item and create YouTube URLs\nconst results = items.map(item => {\n    const VIDEO_ID = item.json.VIDEO_ID;\n    const GOOGLE_API_KEY = item.json.GOOGLE_API_KEY;\n\n    if (!VIDEO_ID) {\n        throw new Error('The video ID parameter is empty.');\n    }\n\n    if (!GOOGLE_API_KEY) {\n        throw new Error('The Google API Key is missing.');\n    }\n\n    // Construct the API URL with the video ID and dynamically retrieved API key\n    const youtubeUrl = `${BASE_URL}?part=snippet,contentDetails,status,statistics,player,topicDetails&id=${VIDEO_ID}&key=${GOOGLE_API_KEY}`;\n\n    return {\n        json: {\n            youtubeUrl: youtubeUrl\n        }\n    };\n});\n\n// Return array of results\nreturn results;\n\n"}, "typeVersion": 2}, {"id": "1941d169-f91f-4500-af55-deb7a5b2bc23", "name": "Get YouTube Video Details", "type": "n8n-nodes-base.httpRequest", "position": [1580, -220], "parameters": {"url": "={{ $json.youtubeUrl }}", "options": {}}, "typeVersion": 4.2}, {"id": "ca49479c-b8e6-44db-a021-2b0f27a16bfc", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [1280, -340], "parameters": {"color": 3, "width": 680, "height": 300, "content": "## YouTube Video Details\nhttps://developers.google.com/youtube/v3/docs\nhttps://www.googleapis.com/youtube/v3/videos"}, "typeVersion": 1}, {"id": "8224915d-c7c4-449d-8210-463b5c5c39f0", "name": "Workflow Variables", "type": "n8n-nodes-base.set", "position": [1020, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e656b8ef-4266-4f50-bd41-703b4bdb04df", "name": "GOOGLE_API_KEY", "type": "string", "value": "[Add-Your-Google-API-Key-Here]"}, {"id": "32db428d-a2e2-48a0-92c6-3880e744d140", "name": "VIDEO_ID", "type": "string", "value": "={{ $json.id.split(\":\").last() }}"}]}}, "typeVersion": 3.4}, {"id": "cf334637-5632-4d2c-85b9-5ff232e2a164", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [900, -420], "parameters": {"width": 340, "height": 380, "content": "## 💡 YouTube Variables\nhttps://cloud.google.com/docs/get-started/access-apis\n\n- GOOGLE_API_KEY (🌟Add your api key)\n- VIDEO_ID"}, "typeVersion": 1}, {"id": "290bbc35-3835-4f50-9e02-bac8414f35bb", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1480, 580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "fd65592b-e64a-445e-b090-1fecd15de9c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1020, 420], "parameters": {"mode": "combine", "options": {}, "advanced": true, "joinMode": "enrichInput1", "mergeByFields": {"values": [{"field1": "items[0].id", "field2": "id"}]}}, "typeVersion": 3}, {"id": "a3f82f16-5b34-4935-a3a0-ccd7f107eb80", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1480, 1020], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "998fb87b-100c-4b93-bddf-4e09ccc7f312", "name": "Default YouTube Channel Ids", "type": "n8n-nodes-base.set", "position": [260, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "73b9220c-d701-4a29-8aaf-3732d1db0ce6", "name": "Default YouTube Channel Ids", "type": "array", "value": "=[ \"UCTwwnM-YB8zWC0RWwhO5sGw\", \"UCMcoud_ZW7cfxeIugBflSBw\", \"UCtevzRsHEKhs-RK8pAqwSyQ\"]"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "96ca1617-1ee3-4ffa-ae63-d90fc07484c5", "name": "YouTube Channel Ids", "type": "n8n-nodes-base.set", "position": [60, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e01feba6-36c0-4fbe-803b-927069b56506", "name": "YouTube Channel Ids", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "0b5ac0a0-b4cc-43a8-8417-e488b9668c9d", "name": "RSS Read  - Max 15 Latest Videos per Channel", "type": "n8n-nodes-base.rssFeedRead", "position": [260, 420], "parameters": {"url": "={{ $json.rss_feed_url }}", "options": {"ignoreSSL": false}}, "typeVersion": 1.1}, {"id": "474daccd-01ed-4c9e-9c92-be33376b5770", "name": "Label New Videos", "type": "n8n-nodes-base.code", "position": [460, 420], "parameters": {"jsCode": "/**\n * Processes YouTube video items and adds recent_videos flag based on publication date\n * @param {Object[]} $input.all() - Array of input items from previous node\n * @param {number} days - Number of days to check for recent videos (default: 3)\n * @returns {Object[]} - Processed video items with additional properties\n */\ntry {\n  // Get all input items from previous node\n  const items = $input.all();\n  \n  // Define the threshold for recent videos (in days)\n  const days = 3;\n\n  // Validate inputs\n  if (!Array.isArray(items)) {\n    throw new Error('Input must be an array of items');\n  }\n\n  // Process each video item\n  const videos = items.map((item, index) => {\n    try {\n      // Validate required properties\n      if (!item?.json?.id || !item?.json?.pubDate) {\n        throw new Error(`Missing required properties in item ${index}`);\n      }\n\n      // Extract YouTube video ID from the full ID string\n      const videoId = item.json.id.split(':')[2];\n\n      // Calculate if video is recent based on publication date\n      const pubDate = new Date(item.json.pubDate);\n      const thresholdDate = DateTime.now()\n        .setZone('America/New_York')\n        .startOf('day')\n        .minus({days: days});\n\n      // Return processed item with additional properties\n      return {\n        json: {\n          ...item.json,\n          id: videoId,\n          recent_videos: pubDate > new Date(thresholdDate)\n        }\n      };\n    } catch (itemError) {\n      // Handle individual item processing errors\n      console.error(`Error processing item ${index}:`, itemError.message);\n      // Return original item if processing fails\n      return item;\n    }\n  });\n\n  return videos;\n\n} catch (error) {\n  // Handle general execution errors\n  console.error('Error in code execution:', error.message);\n  throw new Error(`Failed to process video items: ${error.message}`);\n}\n"}, "typeVersion": 2}, {"id": "0a47e235-33e5-4b74-8b54-2c97d25fb55c", "name": "Get New Videos", "type": "n8n-nodes-base.filter", "position": [660, 420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "914cc748-6fc4-4031-8e8c-849657b7e661", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.recent_videos }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "d7d6319e-e3c9-44ec-b178-15f2f98f7a8c", "name": "Prepare For Telegram Message", "type": "n8n-nodes-base.set", "position": [1380, 80], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "dea3ff12-0650-474e-aa9e-e0912cb971de", "name": "items[0].id", "type": "string", "value": "={{ $json.items[0].id }}"}, {"id": "11c57b3a-d958-4dda-a52c-91e45b530eaf", "name": "items[0].snippet.title", "type": "string", "value": "={{ $json.items[0].snippet.title }}"}, {"id": "b519c5a4-cef1-46e7-8a33-138262e989e4", "name": "items[0].snippet.thumbnails.standard.url", "type": "string", "value": "={{ $json.items[0].snippet.thumbnails.standard.url }}"}, {"id": "62c5158a-bd64-428b-b681-0add1c8a2177", "name": "link", "type": "string", "value": "={{ $json.link }}"}]}}, "typeVersion": 3.4}, {"id": "30313019-1973-450a-92fa-00d0f0c4480e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1280, 0], "parameters": {"color": 5, "width": 520, "height": 280, "content": "## Send Latest Videos as Telegram Message"}, "typeVersion": 1}, {"id": "215e6ee8-af50-4b42-ac21-64dcef41d9f1", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1280, 320], "parameters": {"color": 6, "width": 680, "height": 400, "content": "## Send Email for Each Latest Video (Multiple Emails)"}, "typeVersion": 1}, {"id": "df46f216-d365-4229-884c-95925ed1c3b6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1280, 760], "parameters": {"color": 6, "width": 680, "height": 400, "content": "## Send Email with a List of Latest Videos (One email only)"}, "typeVersion": 1}, {"id": "ef415f16-fb77-441f-9f34-c1962da8f669", "name": "One List Object", "type": "n8n-nodes-base.aggregate", "position": [1020, 860], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "77b37e2f-eb9c-4b85-84db-a59525390d10", "name": "Prepare YouTube Data", "type": "n8n-nodes-base.set", "position": [1780, -220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b8e16aa1-b2a7-46c2-8d0a-5a6d203f8902", "name": "items[0].id", "type": "string", "value": "={{ $json.items[0].id }}"}, {"id": "560c5991-8aed-474b-99fa-2660ccb5ab8f", "name": "items[0].snippet.title", "type": "string", "value": "={{ $json.items[0].snippet.title }}"}, {"id": "dee0a454-56d3-4c17-83d3-2e3a368414af", "name": "items[0].snippet.description", "type": "string", "value": "={{ $json.items[0].snippet.description }}"}, {"id": "fba2482f-9cc0-4678-b035-f51367a6bff1", "name": "items[0].player.embedHtml", "type": "string", "value": "={{ $json.items[0].player.embedHtml }}"}, {"id": "21f47e6a-6847-4c54-87b1-08953d640011", "name": "items[0].snippet.thumbnails.standard", "type": "object", "value": "={{ $json.items[0].snippet.thumbnails.standard }}"}]}}, "typeVersion": 3.4}, {"id": "b0428f2b-e565-4592-9350-f70a3bcab255", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-20, 320], "parameters": {"color": 3, "width": 880, "height": 300, "content": "## Create YouTube RSS Feed from Favorite Channel Ids"}, "typeVersion": 1}, {"id": "b5fb976b-6f08-4e6d-8d49-821bbb24230a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-20, 0], "parameters": {"width": 880, "height": 280, "content": "## Prepare the List of YouTube Channel Ids"}, "typeVersion": 1}, {"id": "c871e70f-187d-46e4-b012-f8e4318b066f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-20, -300], "parameters": {"color": 4, "width": 280, "height": 260, "content": "## 👍Try Me!"}, "typeVersion": 1}, {"id": "d19e2874-2d5b-4583-9345-d2f10e0b991e", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-340, -300], "parameters": {"color": 4, "width": 280, "height": 260, "content": "## ⌚Set Your Schedule"}, "typeVersion": 1}, {"id": "b295807d-15aa-4a72-8e96-31d21534987a", "name": "Create List of Channel Ids", "type": "n8n-nodes-base.splitOut", "position": [660, 80], "parameters": {"options": {"destinationFieldName": "youtube_channel_id"}, "fieldToSplitOut": "ids"}, "typeVersion": 1}, {"id": "f8c8896e-474b-4213-a3d2-cc737e81e37f", "name": "Every Day", "type": "n8n-nodes-base.scheduleTrigger", "position": [-240, -220], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "347c55fa-24b9-46c9-a44a-786bb84cb300", "name": "Multiple Emails", "type": "n8n-nodes-base.gmail", "position": [1740, 420], "webhookId": "c016d26a-1c8a-4564-b715-a65bfcc902ea", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.text }}", "options": {}, "subject": "Latest YouTube Videos from Your Favorite Channels"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "3d5135db-4603-487e-ae70-4c457719b217", "name": "Single Email", "type": "n8n-nodes-base.gmail", "position": [1740, 860], "webhookId": "c016d26a-1c8a-4564-b715-a65bfcc902ea", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.text }}", "options": {}, "subject": "Latest YouTube Videos from Your Favorite Channels"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "3282d84b-497c-4a1c-90d7-7baabe614ca9", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-640, -460], "parameters": {"color": 7, "width": 2640, "height": 1660, "content": "# 🎦💌 Advanced YouTube RSS Feed Buddy for Your Favorite Channels\n## Automated Telegram and Email Notificatons for Latest YouTube Videos from Custom YouTube RSS Feeds"}, "typeVersion": 1}, {"id": "4b00df24-e3bb-44d0-afd5-be7afe272a6e", "name": "Create Email per Video", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1380, 420], "parameters": {"text": "=Create a list of responsive HTML email cards with the following requirements:\n\n## Use the following JSON data to populate the content:\n{{ $json.items.toJsonString() }}\n\n## Design requirements:\n- Use the HTML Card Template example provided which uses a clean, modern card layout\n- Use safe email-compatible HTML and inline CSS\n- Include padding and margins for readability\n- Make the title clickable and link to the YouTube URL\n- Display the author name and publication date\n- Use web-safe fonts\n- Ensure the card is mobile-responsive\n- Keep the design simple and professional\n- Add a YouTube play button icon or indication that it's a video\n\n3. Output only the HTML code without any preample or further explanations.  Remove all ``` and ```html from the response.\n\nThe HTML should be optimized for email clients and follow email HTML best practices.\n\n### HTML Card Template example:\n\n<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\\n  <tr>\\n    <td align=\"center\">\\n      <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"max-width: 100%; border: 1px solid #e0e0e0; border-radius: 8px; font-family: Arial, sans-serif; background-color: #ffffff;\">\\n        <tr>\\n          <td style=\"padding: 16px;\">\\n            <img src=\"https://i.ytimg.com/vi/gTZOxYV379M/default.jpg\" alt=\"Video Thumbnail\" style=\"width: 100%; border-radius: 8px; display: block;\"/>\\n            <h2 style=\"font-size: 20px; margin: 12px 0; color: #333;\"><a href=\"//www.youtube.com/embed/gTZOxYV379M\" style=\"text-decoration: none; color: #1a73e8;\">n8n Tutorial #10: Two n8n features to build AI Agents faster & easier</a></h2>\\n            <p style=\"color: #555; font-size: 14px; line-height: 1.5; margin: 8px 0;\">Discover how to build AI Agents and Tools faster and easier using n8n's built in <span style=\"font-weight: bold;\">$fromAI()</span> function as well as their Easy button. Find out which solution may be better for you.</p>\\n            <div style=\"padding: 10px 0;\">\\n              <p style=\"color: #888; font-size: 12px; margin: 0;\">👨‍💼 Business Inquiries: <a href=\"mailto:<EMAIL>\" style=\"color: #1a73e8; text-decoration: none;\"><EMAIL></a></p>\\n              <p style=\"color: #888; font-size: 12px; margin: 0;\">Timestamps: 0:00 Intro | 0:35 The hack | 1:13 Solution 1: Easy | 1:30 Demo | 3:11 $fromAI()</p>\\n            </div>\\n            <div style=\"text-align: center; margin-top: 8px;\">\\n              <a href=\"//www.youtube.com/embed/gTZOxYV379M\" style=\"display: inline-block; background-color: #ff0000; color: #ffffff; padding: 10px 15px; border-radius: 4px; text-decoration: none; font-weight: bold;\">▶ Play Video</a>\\n            </div>\\n          </td>\\n        </tr>\\n      </table>\\n    </td>\\n  </tr>\\n</table>\n", "promptType": "define"}, "typeVersion": 1.5}, {"id": "3b50b7c2-8a2a-4383-9f61-757fc1db61c0", "name": "Create One Email for All Videos", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1380, 860], "parameters": {"text": "=Create a list of responsive HTML email cards with the following requirements:\n\n## Use the following JSON data to populate the content:\n{{ $json.data.toJsonString() }}\n\n## Design requirements:\n- Use the HTML Card Template example provided which uses a clean, modern card layout\n- Use safe email-compatible HTML and inline CSS\n- Include padding and margins for readability\n- Make the title clickable and link to the YouTube URL\n- Display the author name and publication date\n- Use web-safe fonts\n- Ensure the card is mobile-responsive\n- Keep the design simple and professional\n- Add a YouTube play button icon or indication that it's a video\n\n3. Output only the HTML code without any preample or further explanations.  Remove all ``` and ```html from the response.\n\nThe HTML should be optimized for email clients and follow email HTML best practices.\n\n### HTML Card Template example:\n\n<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\\n  <tr>\\n    <td align=\"center\">\\n      <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"max-width: 100%; border: 1px solid #e0e0e0; border-radius: 8px; font-family: Arial, sans-serif; background-color: #ffffff;\">\\n        <tr>\\n          <td style=\"padding: 16px;\">\\n            <img src=\"https://i.ytimg.com/vi/gTZOxYV379M/default.jpg\" alt=\"Video Thumbnail\" style=\"width: 100%; border-radius: 8px; display: block;\"/>\\n            <h2 style=\"font-size: 20px; margin: 12px 0; color: #333;\"><a href=\"//www.youtube.com/embed/gTZOxYV379M\" style=\"text-decoration: none; color: #1a73e8;\">n8n Tutorial #10: Two n8n features to build AI Agents faster & easier</a></h2>\\n            <p style=\"color: #555; font-size: 14px; line-height: 1.5; margin: 8px 0;\">Discover how to build AI Agents and Tools faster and easier using n8n's built in <span style=\"font-weight: bold;\">$fromAI()</span> function as well as their Easy button. Find out which solution may be better for you.</p>\\n            <div style=\"padding: 10px 0;\">\\n              <p style=\"color: #888; font-size: 12px; margin: 0;\">👨‍💼 Business Inquiries: <a href=\"mailto:<EMAIL>\" style=\"color: #1a73e8; text-decoration: none;\"><EMAIL></a></p>\\n              <p style=\"color: #888; font-size: 12px; margin: 0;\">Timestamps: 0:00 Intro | 0:35 The hack | 1:13 Solution 1: Easy | 1:30 Demo | 3:11 $fromAI()</p>\\n            </div>\\n            <div style=\"text-align: center; margin-top: 8px;\">\\n              <a href=\"//www.youtube.com/embed/gTZOxYV379M\" style=\"display: inline-block; background-color: #ff0000; color: #ffffff; padding: 10px 15px; border-radius: 4px; text-decoration: none; font-weight: bold;\">▶ Play Video</a>\\n            </div>\\n          </td>\\n        </tr>\\n      </table>\\n    </td>\\n  </tr>\\n</table>\n", "promptType": "define"}, "typeVersion": 1.5}, {"id": "0a6bc236-1e8d-43bf-ac80-483d13531b06", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1580, 80], "webhookId": "********-02c0-42ee-98c3-a2ec72b3bf12", "parameters": {"file": "={{ $json.items[0].snippet.thumbnails.standard.url }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "operation": "sendPhoto", "additionalFields": {"caption": "=New YouTube Video From Your Favorite Channel {{ $json.items[0].snippet.title }} {{ $json.items[0].snippet.thumbnails.standard.url }} {{ $json.link }}"}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "e2a39e5e-2df9-4165-92e5-ed7b4f3837ce", "name": "Create RSS Feed URLs", "type": "n8n-nodes-base.set", "position": [460, 860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8159d367-513c-406b-8ad7-36f65c2e6512", "name": "rss_feed_url", "type": "string", "value": "=https://www.youtube.com/feeds/videos.xml?channel_id={{ $json.youtube_channel_id }}"}]}}, "typeVersion": 3.4}, {"id": "228acccf-7d8f-4b07-9b0d-e88e3284a4c5", "name": "RSS Read  - Max 15 Latest Videos per Channel1", "type": "n8n-nodes-base.rssFeedRead", "position": [660, 860], "parameters": {"url": "={{ $json.rss_feed_url }}", "options": {"ignoreSSL": false}}, "typeVersion": 1.1}, {"id": "c6f2cd3b-7920-4f1d-a2c2-299dcd4ef592", "name": "Create List of Channel Ids1", "type": "n8n-nodes-base.splitOut", "position": [260, 860], "parameters": {"options": {"destinationFieldName": "youtube_channel_id"}, "fieldToSplitOut": "ids"}, "typeVersion": 1}, {"id": "e1a040f0-513c-4810-9b6d-9db5a4ac64a5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-20, 740], "parameters": {"color": 2, "width": 880, "height": 320, "content": "## Simple Option for Creating YouTube RSS Feed by Channel Ids"}, "typeVersion": 1}, {"id": "b1229199-464c-42e0-b8c2-8cc58bebfeb0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [-200, 660], "parameters": {}, "typeVersion": 1}, {"id": "5b271425-aa7e-4703-a89f-64f10d6396dc", "name": "YouTube Channel Ids1", "type": "n8n-nodes-base.set", "position": [60, 860], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "73b9220c-d701-4a29-8aaf-3732d1db0ce6", "name": "ids", "type": "array", "value": "=[ \"UCTwwnM-YB8zWC0RWwhO5sGw\", \"UCMcoud_ZW7cfxeIugBflSBw\", \"UCtevzRsHEKhs-RK8pAqwSyQ\"]"}]}}, "typeVersion": 3.4}, {"id": "d07a4c79-9035-4eab-84c8-8ab31454471f", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-600, 0], "parameters": {"width": 540, "height": 1060, "content": "## 🎯 Description\n\nThis workflow creates an automated system for monitoring and receiving notifications about new videos from your favorite YouTube channels through RSS feeds, with customizable email and Telegram notifications.\n\n## 🌟 Key Features\n**📡 RSS Feed Management**\n- Accepts custom YouTube channel IDs or uses default channels\n- Automatically creates RSS feeds for each YouTube channel\n- Monitors channels for new video uploads\n- Labels and filters recent videos within a 3-day window (change this as required)\n\n\n**📨 Multi-Channel Notification System**\n- Sends Telegram notifications with video thumbnails and links\n- Delivers customized email notifications in two formats:\n  - Individual emails for each new video\n  - Single digest email containing all new videos\n\n\n**⚙️ Content Processing**\n- Fetches detailed video information using YouTube API\n- Creates responsive HTML email templates with video previews\n- Includes video thumbnails, titles, descriptions, and direct links\n- Maintains professional formatting across different email clients\n\n\n## 🛠️ Setup Requirements\n**🔑 API Configuration**\n- YouTube Data API credentials\n- Gmail account for sending notifications\n- Telegram bot token and chat ID\n- OpenAI API key for content processing\n\n\n**📋 Channel Management**\n- Add YouTube channel IDs through form input\n- Configure default channel list\n- Set notification preferences\n- Adjust monitoring schedule\n\n\nThis workflow is perfect for content creators, marketers, or anyone wanting to stay updated with their favorite YouTube channels through automated, professionally formatted notifications delivered via email and Telegram.\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "7d101e72-043a-42f0-a28a-4253d204869e", "connections": {"Merge": {"main": [[{"node": "Create Email per Video", "type": "main", "index": 0}, {"node": "One List Object", "type": "main", "index": 0}, {"node": "Prepare For Telegram Message", "type": "main", "index": 0}]]}, "Every Day": {"main": [[{"node": "YouTube Channel Ids", "type": "main", "index": 0}]]}, "Get New Videos": {"main": [[{"node": "Workflow Variables", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get Channel Ids": {"main": [[{"node": "Create List of Channel Ids", "type": "main", "index": 0}]]}, "One List Object": {"main": [[{"node": "Create One Email for All Videos", "type": "main", "index": 0}]]}, "Label New Videos": {"main": [[{"node": "Get New Videos", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Create Email per Video", "type": "ai_languageModel", "index": 0}]]}, "On form submission": {"main": [[{"node": "Default YouTube Channel Ids", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Create One Email for All Videos", "type": "ai_languageModel", "index": 0}]]}, "Workflow Variables": {"main": [[{"node": "Create YouTube API URL", "type": "main", "index": 0}]]}, "YouTube Channel Ids": {"main": [[{"node": "Default YouTube Channel Ids", "type": "main", "index": 0}]]}, "Create RSS Feed URLs": {"main": [[{"node": "RSS Read  - Max 15 Latest Videos per Channel1", "type": "main", "index": 0}]]}, "Prepare YouTube Data": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "YouTube Channel Ids1": {"main": [[{"node": "Create List of Channel Ids1", "type": "main", "index": 0}]]}, "Create RSS Feed URLs1": {"main": [[{"node": "RSS Read  - Max 15 Latest Videos per Channel", "type": "main", "index": 0}]]}, "Create Email per Video": {"main": [[{"node": "Multiple Emails", "type": "main", "index": 0}]]}, "Create YouTube API URL": {"main": [[{"node": "Get YouTube Video Details", "type": "main", "index": 0}]]}, "Get YouTube Video Details": {"main": [[{"node": "Prepare YouTube Data", "type": "main", "index": 0}]]}, "Create List of Channel Ids": {"main": [[{"node": "Create RSS Feed URLs1", "type": "main", "index": 0}]]}, "Create List of Channel Ids1": {"main": [[{"node": "Create RSS Feed URLs", "type": "main", "index": 0}]]}, "Default YouTube Channel Ids": {"main": [[{"node": "Get Channel Ids", "type": "main", "index": 0}]]}, "Prepare For Telegram Message": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Create One Email for All Videos": {"main": [[{"node": "Single Email", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "YouTube Channel Ids1", "type": "main", "index": 0}]]}, "RSS Read  - Max 15 Latest Videos per Channel": {"main": [[{"node": "Label New Videos", "type": "main", "index": 0}]]}}}