{"id": "OvuZIXwt9mdU2JGK", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a", "templateCredsSetupCompleted": true}, "name": "FLUX-fill standalone", "tags": [], "nodes": [{"id": "9f051c89-0243-48fb-baa4-666af3fe54b3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [940, 120], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "5da963f7-4320-4359-aefa-bf8f6d6ef815", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1520, 120], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.html }}"}, "typeVersion": 1.1}, {"id": "05d877bc-b591-478c-b112-32b7efe1ca3f", "name": "Wait 3 sec", "type": "n8n-nodes-base.wait", "position": [920, 680], "webhookId": "90f31c1f-6707-4f2f-b525-d3961432cd81", "parameters": {"amount": 3}, "typeVersion": 1.1}, {"id": "a3cc4a50-4218-4a01-ab20-151fd707dd66", "name": "Is Ready?", "type": "n8n-nodes-base.if", "position": [1340, 680], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3cf5b451-9ff5-4c2a-864f-9aa7d286871a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "Ready"}]}}, "typeVersion": 2.2}, {"id": "76a2dcd4-0e57-461d-a8b9-8f52baa3f86a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [520, -100], "parameters": {"width": 1193, "height": 479, "content": "# Deliver the editor with links to the images"}, "typeVersion": 1}, {"id": "b32e8e0b-a449-47d9-8de4-c0062235ff99", "name": "FLUX Fill", "type": "n8n-nodes-base.httpRequest", "position": [660, 680], "parameters": {"url": "https://api.bfl.ml/v1/flux-pro-1.0-fill", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.body.prompt }}"}, {"name": "steps", "value": "={{ $json.body.steps }}"}, {"name": "prompt_upsampling", "value": "={{ $json.body.prompt_upsampling }}"}, {"name": "guidance", "value": "={{ $json.body.guidance }}"}, {"name": "output_format", "value": "png"}, {"name": "safety_tolerance", "value": "6"}, {"name": "image", "value": "={{ $json.body.image.split(',')[1] }}"}, {"name": "mask", "value": "={{ $json.body.mask.split(',')[1] }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "4eQN9wBw8SniKcPw", "name": "bfl-FLUX"}}, "typeVersion": 4.2}, {"id": "d7d70191-5316-4f20-b570-b8f138b77762", "name": "Check FLUX status", "type": "n8n-nodes-base.httpRequest", "position": [1120, 680], "parameters": {"url": "https://api.bfl.ml/v1/get_result", "options": {}, "sendQuery": true, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.id }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "4eQN9wBw8SniKcPw", "name": "bfl-FLUX"}}, "typeVersion": 4.2}, {"id": "dafc2712-114f-4723-b587-08ff853513f5", "name": "Get Fill Image", "type": "n8n-nodes-base.httpRequest", "position": [1560, 780], "parameters": {"url": "={{ $json.result.sample }}", "options": {}}, "typeVersion": 4.2}, {"id": "68672890-62c3-4020-a09c-9ea691cba361", "name": "Show the image to user", "type": "n8n-nodes-base.respondToWebhook", "position": [1900, 780], "parameters": {"options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "={{ $binary.data.mimeType }}"}]}}, "respondWith": "binary", "responseDataSource": "set"}, "typeVersion": 1.1}, {"id": "7546ce49-56e9-44fd-96fd-324831f38f32", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [560, 420], "parameters": {"color": 4, "width": 1142, "height": 502, "content": "# Image processing part"}, "typeVersion": 1}, {"id": "cee89c8c-7b88-4cc5-84e4-eb7b404e5042", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, 660], "parameters": {"width": 506, "height": 272, "content": "# Send back edited image\n## Add extra steps to save an edited image"}, "typeVersion": 1}, {"id": "a340cd78-56dd-4ac8-a1c1-f3fc03771ae6", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [660, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "20c39c67-3cf8-4e29-b871-3202f2e20a3c", "name": "Images", "type": "array", "value": "={{\n[\n{\"url\":\"https://byuroscope.fra1.digitaloceanspaces.com/nc/uploads/noco/fluxtest/creative-arrangement-minimalist-podium_23-2148959328.jpg\",\n \"title\":\"Stage\" },\n{\"url\":\"https://byuroscope.fra1.digitaloceanspaces.com/nc/uploads/noco/fluxtest/Standing-Big-Paper-Bag-Mockup.jpg\",\n \"title\":\"Paper Bag\" },\n{\"url\":\"https://byuroscope.fra1.digitaloceanspaces.com/nc/uploads/noco/fluxtest/Ceramic-Mug-on-Table-Mockup.jpg\",\n \"title\":\"Big Mug\" },\n{\"url\":\"https://byuroscope.fra1.digitaloceanspaces.com/nc/uploads/noco/fluxtest/Transparent-Bottle-on-Sunny-Beach-Mockup-D.jpg\",\n \"title\":\"Transparent-Bottle\" },\n{\"url\":\"https://byuroscope.fra1.digitaloceanspaces.com/nc/uploads/noco/fluxtest/skin-products-arrangement-wooden-blocks_23-2148761445.jpg\",\n \"title\":\"Cosmetics\" }\n]\n}}"}]}}, "typeVersion": 3.4}, {"id": "da82cb73-af4a-4042-bf4e-17894155fb87", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [260, 120], "webhookId": "9c864ee6-e4d3-46e7-98d4-bea43739963e", "parameters": {"path": "flux-fill", "options": {}, "responseMode": "responseNode", "multipleMethods": true}, "typeVersion": 2}, {"id": "0f35da2f-112c-45f9-9cbe-d64eb8bdc6d8", "name": "Editor page", "type": "n8n-nodes-base.html", "position": [1240, 120], "parameters": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n <meta charset=\"UTF-8\">\n <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n <title>Konva Image Editor</title>\n <script src=\"https://unpkg.com/konva@9/konva.min.js\"></script>\n <script defer src=\"https://unpkg.com/img-comparison-slider@8/dist/index.js\"></script>\n <link rel=\"stylesheet\" href=\"https://unpkg.com/img-comparison-slider@8/dist/styles.css\" />\n <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/gh/ed-parsadanyan/n8n-flux-fill-demo/flux-fill-style.css\" />\n <script src=\"https://cdn.jsdelivr.net/gh/ed-parsadanyan/n8n-flux-fill-demo/flux-fill-canvas.js\"></script>\n</head>\n<body>\n <div class=\"controls-wrapper\">\n <div class=\"left-panel\">\n <div class=\"image-controls\">\n <select id=\"imageSelector\">\n <option value=\"\">Select an image...</option>\n <option value=\"local\">Load from PC...</option>\n </select>\n <input type=\"file\" id=\"fileInput\" style=\"display: none\" accept=\"image/*\">\n <button id=\"clearButton\">Clear All</button>\n </div>\n \n <div class=\"brush-controls\">\n <label for=\"brushSize\" title=\"Use mouse wheel to adjust brush size\">Brush Size:</label>\n <div class=\"slider-container\">\n <input type=\"range\" id=\"brushSize\" min=\"5\" max=\"40\" value=\"20\">\n <span class=\"slider-value\" id=\"brushSizeValue\">20px</span>\n </div>\n </div>\n </div>\n\n <div class=\"right-panel\">\n <div class=\"prompt-row\">\n <input type=\"text\" id=\"promptInput\" placeholder=\"Enter your prompt (optional)\">\n </div>\n \n <div class=\"main-controls\">\n <label class=\"checkbox-container\">\n <input type=\"checkbox\" id=\"improvePrompt\" checked>\n <span>Improve prompt</span>\n </label>\n \n <div>\n <button id=\"sendButton\">Generate</button>\n <span class=\"loading\" id=\"loadingIndicator\">Processing...</span>\n </div>\n </div>\n \n <div class=\"parameters\">\n <div class=\"slider-container\">\n <label for=\"stepsSlider\">Steps:</label>\n <input type=\"range\" id=\"stepsSlider\" min=\"15\" max=\"50\" value=\"40\">\n <span class=\"slider-value\" id=\"stepsValue\">40</span>\n </div>\n \n <div class=\"slider-container\">\n <label for=\"guidanceSlider\">Guidance:</label>\n <input type=\"range\" id=\"guidanceSlider\" min=\"1.5\" max=\"100\" value=\"60\" step=\"0.1\">\n <span class=\"slider-value\" id=\"guidanceValue\">60.0</span>\n </div>\n </div>\n </div>\n </div>\n\n <div class=\"info\" id=\"imageInfo\"></div>\n <div id=\"container\"></div>\n <div id=\"cursor\"></div>\n\n <div id=\"resultModal\" class=\"modal\">\n <div class=\"modal-content\">\n <div class=\"modal-image-container\">\n <div class=\"comparison-container\">\n <div class=\"image-container\">\n <img class=\"image-before\" id=\"originalImage\" src=\"\" alt=\"Original\">\n <img class=\"image-after\" id=\"resultImage\" src=\"\" alt=\"Generated\">\n </div>\n <input type=\"range\" min=\"0\" max=\"100\" value=\"10\" class=\"slider\">\n <div class=\"slider-line\"></div>\n <div class=\"slider-button\" aria-hidden=\"true\">\n &lt; &gt;\n </div>\n <div class=\"labels\">\n <div class=\"label-before\">Original</div>\n <div class=\"label-after\">Generated</div>\n </div>\n </div>\n </div>\n <div class=\"modal-buttons\">\n <button id=\"reuseButton\">Use Generated</button>\n <button id=\"saveButton\">Save Image</button>\n <button id=\"closeButton\">Close</button>\n </div>\n </div>\n </div>\n\n<script>\n const urlParams = new URLSearchParams(window.location.search);\n const pageId = urlParams.get('id');\n\n // Image data will be populated by n8n\n const imageData = {{ JSON.stringify($json.Images,'',2) }};\n const webhookUrl = '{{ $json.webhookUrl }}';\n\n // Initialize the editor when the page loads\n document.addEventListener('DOMContentLoaded', function() {\n initializeEditor({\n images: imageData,\n webhookUrl: webhookUrl,\n pageId: pageId\n });\n });\n</script>\n</body>\n</html>\n"}, "typeVersion": 1.2}, {"id": "2ff87261-8a7f-451e-b8ae-b4274776ce28", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, 20], "parameters": {"color": 5, "width": 360, "height": 340, "content": "## Image array\n* Load from PC\n* Select one of the default images\n\n### Change this node to\n### get image URLs from your data source"}, "typeVersion": 1}, {"id": "08bb17fd-1440-4194-8c4f-e18222a68bf2", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1080, -20], "parameters": {"color": 5, "width": 400, "height": 300, "content": "## HTML code of the editor\n* Konva.js\n* img-comparison-slider to compare edits vs original file\n* Additional css + js files for the editor logic"}, "typeVersion": 1}, {"id": "13a820d0-e83b-4d1e-81d1-738ef8ca4d47", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [580, 500], "parameters": {"color": 5, "width": 280, "height": 340, "content": "## Call FLUX-Fill Tool\nPass the following data:\n* original image\n* alpha mask from the editor\n* text prompt\n* additional settings"}, "typeVersion": 1}, {"id": "f4ab042c-d4da-4f1e-aa05-fdd2cca62d66", "name": "NO OP", "type": "n8n-nodes-base.noOp", "position": [420, 680], "parameters": {}, "typeVersion": 1}], "active": true, "pinData": {"Webhook": []}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": 120, "saveDataSuccessExecution": "all"}, "versionId": "6d4112be-fb6f-4702-ac5f-2c49ff0117d4", "connections": {"Merge": {"main": [[{"node": "Editor page", "type": "main", "index": 0}]]}, "NO OP": {"main": [[{"node": "FLUX Fill", "type": "main", "index": 0}]]}, "Mockups": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Webhook": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "NO OP", "type": "main", "index": 0}]]}, "FLUX Fill": {"main": [[{"node": "Wait 3 sec", "type": "main", "index": 0}]]}, "Is Ready?": {"main": [[{"node": "Get Fill Image", "type": "main", "index": 0}], [{"node": "Wait 3 sec", "type": "main", "index": 0}]]}, "Wait 3 sec": {"main": [[{"node": "Check FLUX status", "type": "main", "index": 0}]]}, "Editor page": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get Fill Image": {"main": [[{"node": "Show the image to user", "type": "main", "index": 0}]]}, "Check FLUX status": {"main": [[{"node": "Is Ready?", "type": "main", "index": 0}]]}}}