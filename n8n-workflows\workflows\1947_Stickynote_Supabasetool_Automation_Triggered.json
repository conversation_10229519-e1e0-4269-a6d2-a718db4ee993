{"id": "oowUGM7ey6gWxzEG", "meta": {"instanceId": "6d46e25379ef430a7067964d1096b885c773564549240cb3ad4c087f6cf94bd3", "templateCredsSetupCompleted": true}, "name": "MCP_SUPABASE_AGENT", "tags": [], "nodes": [{"id": "135ceeee-77cd-479f-a0b4-dd72abe23ac4", "name": "MCP_SUPABASE", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [-1460, 1180], "webhookId": "affff59c-9c5c-4a07-b531-616c1d631601", "parameters": {"path": "affff59c-9c5c-4a07-b531-616c1d631601"}, "typeVersion": 1}, {"id": "b25040a8-2d70-4d3a-ba58-b8c7164d375e", "name": "RAG", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [1240, 760], "parameters": {"mode": "retrieve-as-tool", "topK": 5, "options": {}, "toolName": "ITERACOES", "tableName": {"__rl": true, "mode": "list", "value": "documents", "cachedResultName": "documents"}, "toolDescription": "lembra das interacoes e consulta as instrucoes do system como assim tambem vai guardando o que aprende"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1.1}, {"id": "081035c0-ecc2-4924-8f07-da4cbb69fb06", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1500, 960], "parameters": {"model": "text-embedding-ada-002", "options": {}}, "credentials": {"openAiApi": {"id": "zUnIUrOWA279vAoC", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "361e0a74-b386-4e03-9e7b-5f435f0d8c5f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, 120], "parameters": {"width": 1380, "height": 520, "content": "## AGENT_MESSAGE\n"}, "typeVersion": 1}, {"id": "5aafb3a6-edd1-4154-adab-948db9aad8e7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, 720], "parameters": {"width": 1380, "height": 520, "content": "## AGENT_TASK\n"}, "typeVersion": 1}, {"id": "61b75c2e-b472-4597-a12a-f6027caecf4e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-260, 1320], "parameters": {"width": 1380, "height": 520, "content": "## AGENT_STATUS\n\n\n"}, "typeVersion": 1}, {"id": "7adc4cd9-cbac-4922-b928-f0b556d6f839", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-260, 1900], "parameters": {"width": 1380, "height": 520, "content": "## AGENT_KNOWLEDGE\n\n"}, "typeVersion": 1}, {"id": "7680abd0-d5f1-41db-96ad-d64c1b857032", "name": "DELETE_ROW_INSCRICOES_CURSOS", "type": "n8n-nodes-base.supabaseTool", "position": [260, 2020], "parameters": {"tableId": "agent_knowledge", "operation": "delete"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "5c752cf4-6dde-49d9-9328-2ed0731c6d7a", "name": "GET_ROW_AGENT_MESSAGE", "type": "n8n-nodes-base.supabaseTool", "position": [80, 260], "parameters": {"tableId": "agent_messages", "operation": "get"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "f65e9fd3-a656-473c-a7af-217d9b041aa7", "name": "CREATE_ROW_AGENT_MESSAGE", "type": "n8n-nodes-base.supabaseTool", "position": [-100, 260], "parameters": {"tableId": "agent_messages"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "********-e6ac-4e5b-adb0-fd610cdff8aa", "name": "DELETE_ROW_AGENT_MESSAGE", "type": "n8n-nodes-base.supabaseTool", "position": [260, 260], "parameters": {"tableId": "agent_messages", "operation": "delete"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "52db9de5-5610-4b2d-9194-e1551b95a4e6", "name": "UPDATE_ROW_AGENT_MESSAGE", "type": "n8n-nodes-base.supabaseTool", "position": [440, 260], "parameters": {"tableId": "agent_messages", "operation": "update"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "b43aaea6-7841-4848-9228-2be6dd07a03f", "name": "GET_MANY_ROW_AGENT_MESSAGE", "type": "n8n-nodes-base.supabaseTool", "position": [620, 260], "parameters": {"limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "tableId": "agent_messages", "operation": "getAll"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "c5347c5e-f9cb-40aa-bca5-249e8c220839", "name": "CREATE_ROW_AGENT_TASKS", "type": "n8n-nodes-base.supabaseTool", "position": [-100, 840], "parameters": {"tableId": "agent_tasks"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "85e3c8e1-6a75-40ce-a344-4a8fd3a1ae16", "name": "GET_ROW_AGENT_TASKS", "type": "n8n-nodes-base.supabaseTool", "position": [80, 840], "parameters": {"tableId": "agent_tasks", "operation": "get"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "7dacc138-a3aa-4483-a79c-5f2eee915c72", "name": "DELETE_ROW_AGENT_TASKS", "type": "n8n-nodes-base.supabaseTool", "position": [260, 840], "parameters": {"tableId": "agent_tasks", "operation": "delete"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "cb942ab1-e7f2-4fd7-bc1e-fa9e559480a1", "name": "UPDATE_ROW_AGENT_TASKS", "type": "n8n-nodes-base.supabaseTool", "position": [440, 840], "parameters": {"tableId": "agent_tasks", "operation": "update"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "ed9cc573-764c-4cda-82f4-796851b16fba", "name": "GET_MANY_ROW_AGENT_TASKS", "type": "n8n-nodes-base.supabaseTool", "position": [620, 840], "parameters": {"limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "tableId": "agent_tasks", "operation": "getAll"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "d3412d90-6025-4db5-a845-8b1ea6070ea3", "name": "CREATE_ROW_AGENT_STATUS", "type": "n8n-nodes-base.supabaseTool", "position": [-100, 1440], "parameters": {"tableId": "agent_status"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "843a2b92-8fb4-4453-9517-b37e07148f52", "name": "GET_ROW_AGENT_STATUS", "type": "n8n-nodes-base.supabaseTool", "position": [80, 1440], "parameters": {"tableId": "agent_status", "operation": "get"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "9a075b33-23fa-487c-b139-41e7e4794831", "name": "DELETE_ROW_AGENT_STATUS", "type": "n8n-nodes-base.supabaseTool", "position": [260, 1440], "parameters": {"tableId": "agent_status", "operation": "delete"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "a066b99d-15f4-4c3e-bab6-4423b749bb74", "name": "UPDATE_ROW_AGENT_STATUS", "type": "n8n-nodes-base.supabaseTool", "position": [440, 1440], "parameters": {"tableId": "agent_status", "operation": "update"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "be9930a8-4e01-4823-a0be-4adfd06dd29c", "name": "GET_MANY_ROW_AGENT_STATUS", "type": "n8n-nodes-base.supabaseTool", "position": [620, 1440], "parameters": {"limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "tableId": "agent_status", "operation": "getAll"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "01fbbe34-81e7-4017-a10e-ef7137024d6a", "name": "CREATE_ROW_AGENT_KNOWLEDGE", "type": "n8n-nodes-base.supabaseTool", "position": [-100, 2020], "parameters": {"tableId": "agent_knowledge"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "5ba9e5eb-76bb-499c-b93b-5cca7286259b", "name": "GET_ROW_AGENT_KNOWLEDGE", "type": "n8n-nodes-base.supabaseTool", "position": [80, 2020], "parameters": {"tableId": "agent_knowledge", "operation": "get"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "a25cef14-0cf0-4ded-81f0-cde300f74432", "name": "UPDATE_ROW_INSCRICOES_AGENT_KNOWLEDGE", "type": "n8n-nodes-base.supabaseTool", "position": [440, 2020], "parameters": {"tableId": "agent_knowledge", "operation": "update"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "1c1fae2e-97f9-449f-913a-8ac730c1f145", "name": "GET_MANY_ROW_AGENT_KNOWLEDGE", "type": "n8n-nodes-base.supabaseTool", "position": [620, 2020], "parameters": {"limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "tableId": "agent_knowledge", "operation": "getAll"}, "credentials": {"supabaseApi": {"id": "yfa6fXRKgmrEx175", "name": "Supabase account"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d32edd9b-7508-45a9-adcc-************", "connections": {"RAG": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "RAG", "type": "ai_embedding", "index": 0}]]}, "GET_ROW_AGENT_TASKS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_ROW_AGENT_STATUS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_ROW_AGENT_MESSAGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "CREATE_ROW_AGENT_TASKS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "DELETE_ROW_AGENT_TASKS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "UPDATE_ROW_AGENT_TASKS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "CREATE_ROW_AGENT_STATUS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "DELETE_ROW_AGENT_STATUS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_ROW_AGENT_KNOWLEDGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "UPDATE_ROW_AGENT_STATUS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "CREATE_ROW_AGENT_MESSAGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "DELETE_ROW_AGENT_MESSAGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_MANY_ROW_AGENT_TASKS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "UPDATE_ROW_AGENT_MESSAGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_MANY_ROW_AGENT_STATUS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "CREATE_ROW_AGENT_KNOWLEDGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_MANY_ROW_AGENT_MESSAGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "DELETE_ROW_INSCRICOES_CURSOS": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "GET_MANY_ROW_AGENT_KNOWLEDGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}, "UPDATE_ROW_INSCRICOES_AGENT_KNOWLEDGE": {"ai_tool": [[{"node": "MCP_SUPABASE", "type": "ai_tool", "index": 0}]]}}}