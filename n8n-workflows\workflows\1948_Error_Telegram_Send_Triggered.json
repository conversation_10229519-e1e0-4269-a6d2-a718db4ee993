{"id": "ozo5jlbwPHgaMnVt", "meta": {"instanceId": "2c69a61055797162319204105e5a124e409f0c7fbfaba08ee106324374f4ae73"}, "name": "<PERSON><PERSON><PERSON> send Telegram", "tags": [], "nodes": [{"id": "3968e71e-d9fb-4810-81bb-18ecf073b3ee", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [520, -200], "webhookId": "b3f6e388-8313-4bc1-8077-d81471b2f95d", "parameters": {"text": "=Workflow: {{ $('Error Trigger').first().json.workflow.name }}\nData & Time: {{ $now }}\nURL: {{ $('Error Trigger').first().json.execution.url }}\nLast Node: {{ $('Error Trigger').first().json.execution.lastNodeExecuted }}\nError Detal: {{ $('Error Trigger').first().json.execution.error.message }}\n", "chatId": "={{ $('Config').item.json.telegramChatId }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "BCYwPAl9pdnRqKeR", "name": "Telegram n8n Log Test"}}, "retryOnFail": true, "typeVersion": 1.2, "waitBetweenTries": 3000}, {"id": "bbb54150-b749-49e2-9c49-720341691151", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "position": [60, -200], "parameters": {}, "typeVersion": 1}, {"id": "68bc359d-4c7f-4027-8e76-c2bc6b612ede", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-520, -820], "parameters": {"width": 1420, "height": 1240, "content": "### **How to Use Telegram Error Notifier**\n\n### **Step 1: Prerequisites**\n1. **Telegram <PERSON>t:**\n   - Create a bot using [<PERSON><PERSON><PERSON>ather](https://core.telegram.org/bots#botfather) and get the bot token.\n   - Add the bot to your Telegram group/channel and note the `chatId`.\n\n2. **n8n Setup:**\n   - Ensure the **Telegram** and **Error Trigger** nodes are installed.\n---\n### **Step 2: Configure the Workflow**\n1. **Update Telegram Chat ID:**\n   - Open the **Config** node.\n   - Replace `telegramChatId` with your actual Telegram group/channel ID:\n     ```json\n     return [\n       {\n         \"telegramChatId\": 123456789, // Replace with your chat ID, format 123456789 or -123456789\n       }\n     ];\n     ```\n\n2. **Set Telegram Bot Credentials:**\n   - Open the **Telegram** node.\n   - Add your bot token in the **Credentials** section.\n\n3. **Activate the Workflow:**\n   - Toggle the **Active** switch to enable the workflow.\n---\n### **Step 3: Set Up Error Workflow**\n1. Open the workflow where you want error notifications.\n2. Go to **Settings** > **Error Workflow**.\n3. Select **Telegram Error Notifier** from the dropdown.\n4. Save the changes.\n---\n### **Step 4: Test the Workflow**\n1. Trigger an error in the workflow.\n2. Check your Telegram for the error notification, which includes:\n   - Workflow name\n   - Date and time\n   - Execution URL\n   - Last node executed\n   - Error details\n---\n### **Example Notification**\n```\nWorkflow: My Workflow 1\nData & Time: 2023-10-27T12:34:56Z\nURL: https://n8n.example.com/execution/12345\nLast Node: HTTP Request\nError Detail: Failed to connect to the server.\n```\n---\n### **Troubleshooting**\n- **No Notifications:**  \n  Ensure the workflow is active, and the bot token/chat ID is correct.\n- **Permission Issues:**  \n  Ensure the bot can send messages in your Telegram group/channel.\n---"}, "typeVersion": 1}, {"id": "6bcf5a24-643d-4fbe-81c9-c8830dc8f1b6", "name": "Config", "type": "n8n-nodes-base.set", "position": [300, -200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf7b1294-b50d-49f7-a5f1-76b0d6845aea", "name": "telegramChatId", "type": "string", "value": "123456789"}]}}, "typeVersion": 3.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e3a6d588-a83c-4d4e-afdc-232624479723", "connections": {"Config": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}}}