{"id": "gsra9JToRDftNEvH", "meta": {"instanceId": "e8ec316b54e91908f34cbfdc330e5d1d5e97aa0ea8f7277c00d8a8a3892c9983", "templateCredsSetupCompleted": true}, "name": "🤓 Conversion Rate Optimizer", "tags": [{"id": "QUoce1Blvhtuie7K", "name": "Business", "createdAt": "2025-03-06T15:17:58.728Z", "updatedAt": "2025-03-06T15:17:58.728Z"}], "nodes": [{"id": "8aca34c2-65d6-432a-a7a5-fede59c3f4cb", "name": "Landing Page Url", "type": "n8n-nodes-base.formTrigger", "position": [-180, 0], "webhookId": "0818531a-3892-49f6-af78-cde8d538b205", "parameters": {"options": {}, "formTitle": "Conversion Rate Optimizer", "formFields": {"values": [{"fieldLabel": "Landing Page Url", "placeholder": "https://yuzuu.co", "requiredField": true}]}, "formDescription": "Your Landing Page is Leaking Sales—Fix It Now"}, "typeVersion": 2.2}, {"id": "61e17805-93aa-46a3-a5a1-36c02da6432a", "name": "Scrape Website", "type": "n8n-nodes-base.httpRequest", "position": [20, 0], "parameters": {"url": "={{ $json['Landing Page Url'] }}", "options": {}}, "typeVersion": 4.2}, {"id": "cbe8bed2-37a0-4459-a34c-47b87c012875", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [240, 0], "parameters": {"text": "=You are a professional expert in Conversion Rate Optimization who helps business founders & CMOs improve their landing pages. You are a world-class expert in analysing landing pages, roasting them, and providing valuable Conversion Rate Optimization Ideas to help businesses increase conversions.  \n\nGOAL\nI want you to roast my landing page and deliver recommendations to improve the Conversion Rate. I will use this roast to understand what's wrong with my landing page and make improvements based on your recommendations. \n\nROAST STRUCTURE\nThis framework consists of 2 blocks of insights: \nRoast: a detailed roast of my landing page.\nRecommendations: 10 conversion rate optimization ideas based on your roast and analysis.\n\nROAST & RECOMMENDATIONS CRITERIA\nFor the Roast: Be friendly & casual. Talk like a human to another human. \nFor the Roast: Be unconventional & fun. I don't want to be bored. A roast must agitate the reader's feelings. \nFor the Roast: You will make a full landing page analysis, and explain what's wrong. You will use this analysis to make recommendations for The Recommendations.  \nFor the Recommendations: Be specific. Write exactly what I need to do. Your detailed description for each Conversion Rate Optimization Idea should be self-explanatory. For example, instead of saying “Rewrite your headline”, give me improved ideas for the headline. Your job is to return advanced insights personalised only for my specific landing page. This is a critical law for you.\nFor the Recommendations: Be creative. Don't return trivial and outdated Conversion Rate Optimization ideas that the average marketer would recommend. Prioritise unconventional CRO tactics so I get real value from you here. Think like the top 0.1% conversion rate optimization expert.\nFor the Recommendations: Prioritise Conversion Rate Optimization Ideas that are relevant in the 2024 digital marketing space. \nFor the Recommendations: Your Conversion Rate Optimization ideas must be impactful. Prioritise Conversion Rate Optimization Ideas that adds a wow effect.\nFor the Recommendations: Your Conversion Rate Optimization ideas must be easy to implement.\nFor the Recommendations: Personalise your ideas with references to the Roast you made. I don’t want to read 10 generic ideas that can work for anyone (for example, “add a live chat” or “offer a free trial”). I need a 100% personalised response.\n\nHere is the content of my landing page: {{ $json.data }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "********-d64b-4e84-916e-1df8daeb0287", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 220], "parameters": {"model": {"__rl": true, "mode": "list", "value": "o1", "cachedResultName": "o1"}, "options": {"reasoningEffort": "high"}}, "credentials": {"openAiApi": {"id": "MtyWeuRTqwi3Yx9H", "name": "OpenAi account"}}, "typeVersion": 1.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "38d9dab2-07ed-49cb-836e-a4b3ecf9d7da", "connections": {"AI Agent": {"main": [[]]}, "Scrape Website": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Landing Page Url": {"main": [[{"node": "Scrape Website", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}}