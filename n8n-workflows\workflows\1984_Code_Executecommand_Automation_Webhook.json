{"id": "tlnJNm9t5H3VLU5K", "meta": {"instanceId": "505c2bdb4483cbbca32871c0acd4b60c83809f177e47e2864f71c1c1760a9b2a", "templateCredsSetupCompleted": true}, "name": "Credentials Transfer", "tags": [], "nodes": [{"id": "9bb3fd8d-cead-4325-9c77-2c1d203ac805", "name": "Success", "type": "n8n-nodes-base.form", "position": [680, 300], "webhookId": "6a630d50-a4b6-4fd7-a7a7-fa0283996903", "parameters": {"options": {}, "operation": "completion", "completionTitle": "✅ Transfer completed", "completionMessage": "=The credential has been transfered."}, "typeVersion": 1}, {"id": "0c90e8d6-3230-4139-a625-e8656fe1c4d9", "name": "Error", "type": "n8n-nodes-base.form", "position": [680, 460], "webhookId": "bfaa86b9-fddf-47b3-9456-caba547a1f5e", "parameters": {"options": {}, "operation": "completion", "completionTitle": "⚠️ Transfer failed", "completionMessage": "=Please check the workflow settings"}, "typeVersion": 1}, {"id": "050bb8b6-faf5-4190-a727-986d7fe6b28a", "name": "Create Credential", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [460, 340], "parameters": {"url": "={{ $json.instance.baseUrl }}/credentials", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.credential.name }}"}, {"name": "type", "value": "={{ $json.credential.type }}"}, {"name": "data", "value": "={{ $json.credential.data }}"}]}, "headerParameters": {"parameters": [{"name": "X-N8N-API-KEY", "value": "={{ $json.instance.apiKey }}"}]}}, "typeVersion": 4.2}, {"id": "17072ec2-526c-49b4-8384-e9ca347b8748", "name": "Get Instance Names", "type": "n8n-nodes-base.code", "position": [680, -240], "parameters": {"jsCode": "dropDownValues = [];\n\nfor (const instance of $input.first().json.remoteInstances) {\n  dropDownValues.push({\"option\": instance.name});\n}\n\nreturn { \"options\": JSON.stringify(dropDownValues) };"}, "typeVersion": 2}, {"id": "27fdd4c4-5c39-497b-91b3-e468b20e8f8c", "name": "Settings", "type": "n8n-nodes-base.set", "position": [460, -240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8a5d50fc-95dc-40b3-a3f2-293521bab29a", "name": "remoteInstances", "type": "array", "value": "=[\n  {\n    \"name\": \"n8n-test-01\",\n    \"apiKey\": \"n8n_api_26b5bb6d39d337bd904f3d89fe88562d456c1cd13af401f490145206f2dc516ffa1fed04a26ae689\",\n    \"baseUrl\": \"https://n8n-test-01.services.octionic.com/api/v1\"\n  }\n]"}]}}, "typeVersion": 3.4}, {"id": "76b7d9d2-c919-42a6-8724-8771d9415e3e", "name": "Export Credentials", "type": "n8n-nodes-base.executeCommand", "position": [240, 40], "parameters": {"command": "n8n export:credentials --all --pretty --decrypted --output=/tmp/cred"}, "typeVersion": 1}, {"id": "b5f53aa1-ccf6-43b0-9f4c-cfc52f99aabf", "name": "Get Credentials Data", "type": "n8n-nodes-base.readWriteFile", "position": [460, 40], "parameters": {"options": {}, "fileSelector": "/tmp/cred"}, "typeVersion": 1}, {"id": "16fdae2d-f3e6-4030-859a-ecba74dfe4c2", "name": "Binary to JSON", "type": "n8n-nodes-base.extractFromFile", "position": [680, 40], "parameters": {"options": {}, "operation": "fromJson"}, "typeVersion": 1}, {"id": "3d590285-e82c-4754-8618-2a52fcda6253", "name": "Get Credential Names", "type": "n8n-nodes-base.code", "position": [900, 40], "parameters": {"jsCode": "dropDownValues = [];\n\nfor (const credential of $input.first().json.data) {\n  dropDownValues.push({\"option\": credential.name});\n}\n\nreturn { \"options\": JSON.stringify(dropDownValues) };"}, "typeVersion": 2}, {"id": "21615934-6834-4b4e-b521-942a24f64388", "name": "Choose Instance", "type": "n8n-nodes-base.form", "position": [900, -240], "webhookId": "b1fc6927-ebe8-4a02-9d64-24bf6f6d0db6", "parameters": {"options": {"formTitle": "Select Destination", "buttonLabel": "Continue", "formDescription": "Choose the n8n instance where the credential should be copied to"}, "defineForm": "json", "jsonOutput": "=[\n   {\n      \"fieldLabel\": \"Destination\",\n      \"fieldType\": \"dropdown\",\n      \"requiredField\": true,\n      \"fieldOptions\": {\n        \"values\": {{ $json.options }}\n      }\n   }\n]"}, "typeVersion": 1}, {"id": "d36a41c4-6ed3-4939-8562-e3f50ffcd72b", "name": "<PERSON>ose Credential", "type": "n8n-nodes-base.form", "position": [1120, 40], "webhookId": "f732b37b-6623-4629-bc7d-99ebb56a9809", "parameters": {"options": {"formTitle": "Select Credential", "buttonLabel": "Copy now", "formDescription": "Choose the credential which should be copied"}, "defineForm": "json", "jsonOutput": "=[\n   {\n      \"fieldLabel\": \"Credential\",\n      \"fieldType\": \"dropdown\",\n      \"requiredField\": true,\n      \"fieldOptions\": {\n        \"values\": {{ $json.options }}\n      }\n   }\n]"}, "typeVersion": 1}, {"id": "4391f0d9-3ed5-4e2b-a761-ab7bf41959f1", "name": "Prepare Request Data", "type": "n8n-nodes-base.code", "position": [240, 340], "parameters": {"jsCode": "output = {};\n\nfor (const credential of $('Binary to JSON').first().json.data) {\n  if (credential.name == $input.first().json.Credential) {\n    output.credential = credential;\n  }\n}\n\nfor (const instance of $('Settings').first().json.remoteInstances) {\n  if (instance.name == $('Choose Instance').first().json.Destination) {\n    output.instance = instance;\n  }\n}\n\nreturn output;"}, "typeVersion": 2}, {"id": "571cd727-218b-4a5d-97fe-a8dcbcf15cce", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [400, -340], "parameters": {"width": 216.47293010628914, "height": 255.86856541619233, "content": "## Setup instances\nEach instnce requires a name, apiKey and baseURL"}, "typeVersion": 1}, {"id": "7f717bed-fac5-4b1e-acc7-a6ad9d1c4be4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-360, -340], "parameters": {"color": 5, "width": 527.8711162255479, "height": 223.19907940161124, "content": "## Instances config example\n```\n[\n  {\n    \"name\": \"n8n-test\",\n    \"apiKey\": \"XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX\",\n    \"baseUrl\": \"https://n8n-test.example.com/api/v1\"\n  },\n  {\n    ...\n  }\n]\n```"}, "typeVersion": 1}, {"id": "5e27ad60-7e65-452d-8c33-56c6c85911d3", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [180, -340], "parameters": {"color": 7, "width": 216.47293010628914, "height": 255.86856541619233, "content": "A form with no input fields initiates the process.\n*Consider securing the form using Basic Auth.*"}, "typeVersion": 1}, {"id": "0b69effa-44e4-41e1-bb5d-934ed19b8488", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [180, -60], "parameters": {"color": 7, "width": 655.5152156976209, "height": 255.86856541619233, "content": "Credentials are being fetched using the cmd tools, since there is no API endpoint available for this. The data is then converted into a JSON object."}, "typeVersion": 1}, {"id": "a0f83646-dcf3-4893-bca0-a2b0ec4942b6", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [620, -340], "parameters": {"color": 7, "width": 435.9715408127852, "height": 255.86856541619233, "content": "A list of the instance names is being extracted from the settings and used as dynamic options for the dropdown in a new form page."}, "typeVersion": 1}, {"id": "78611a9e-5949-44c4-af23-78e8f689de2b", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [840, -60], "parameters": {"color": 7, "width": 435.9715408127852, "height": 255.86856541619233, "content": "A list of the credential names is being extracted from the settings and used as dynamic options for the dropdown in a new form page."}, "typeVersion": 1}, {"id": "23e9b0ea-f6a7-4072-8e87-292ba432a24d", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [180, 240], "parameters": {"color": 7, "width": 216.47293010628914, "height": 255.86856541619233, "content": "Based on the selections made by the user, the necessary data is being provided."}, "typeVersion": 1}, {"id": "f13a3bce-d92d-4e2c-bb63-b37c4aa89b9b", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [400, 240], "parameters": {"color": 7, "width": 216.47293010628914, "height": 255.86856541619233, "content": "The credential data is being transferred to the selected instance based using dynamic authentication"}, "typeVersion": 1}, {"id": "1788ed42-44be-41c3-8a45-3d8c74925ded", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [620, 240], "parameters": {"color": 7, "width": 216.47293010628914, "height": 376.16893354714523, "content": "Display a confirmation/error message to the user"}, "typeVersion": 1}, {"id": "58cbb873-906e-4215-b501-4a1b31fdcbd9", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [240, -240], "webhookId": "f6c627e5-e93c-4ee1-9605-ebdfa5fff286", "parameters": {"options": {"buttonLabel": "<PERSON><PERSON>", "appendAttribution": false}, "formTitle": "Credential Transfer", "formFields": {"values": [{"fieldLabel": " ", "placeholder": "Click \"Begin\" to continue"}]}, "formDescription": "This tool allows copying a credential to another n8n instance"}, "typeVersion": 2.2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e89a409f-c046-4d54-b580-7e2c0a537d1b", "connections": {"Settings": {"main": [[{"node": "Get Instance Names", "type": "main", "index": 0}]]}, "Binary to JSON": {"main": [[{"node": "Get Credential Names", "type": "main", "index": 0}]]}, "Choose Instance": {"main": [[{"node": "Export Credentials", "type": "main", "index": 0}]]}, "Choose Credential": {"main": [[{"node": "Prepare Request Data", "type": "main", "index": 0}]]}, "Create Credential": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Error", "type": "main", "index": 0}]]}, "Export Credentials": {"main": [[{"node": "Get Credentials Data", "type": "main", "index": 0}]]}, "Get Instance Names": {"main": [[{"node": "Choose Instance", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "Get Credential Names": {"main": [[{"node": "<PERSON>ose Credential", "type": "main", "index": 0}]]}, "Get Credentials Data": {"main": [[{"node": "Binary to JSON", "type": "main", "index": 0}]]}, "Prepare Request Data": {"main": [[{"node": "Create Credential", "type": "main", "index": 0}]]}}}