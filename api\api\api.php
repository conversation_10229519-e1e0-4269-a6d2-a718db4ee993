<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');

// --- CONFIG ---
$SETTINGS = [
    "hostname" => '************',
    "mysql_user" => 'icredept_tariq',
    "mysql_pass" => 'Hgd!ld198479',
    "mysql_database" => 'icredept_1comm',
    // Add an API key for basic security (change this!)
    "api_key" => 'Hgd!ld79'
];

// --- ONLY ALLOW POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Only POST method is allowed."]);
    exit();
}

// --- READ JSON BODY ---
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(["error" => "Invalid or missing JSON body."]);
    exit();
}

// --- API KEY CHECK (optional, recommended) ---
if (!isset($input['api_key']) || $input['api_key'] !== $SETTINGS['api_key']) {
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized. Invalid API key."]);
    exit();
}

// --- CHECK FOR DATABASE (optional) ---
$database = $SETTINGS['mysql_database']; // Default database
if (isset($input['database']) && !empty($input['database'])) {
    $database = $input['database']; // Override with user-provided database
}

// --- CHECK FOR QUERY ---
if (!isset($input['query'])) {
    http_response_code(400);
    echo json_encode(["error" => "Missing 'query' in request body."]);
    exit();
}

// --- CONNECT TO MYSQL ---
$conn = new mysqli(
    $SETTINGS['hostname'],
    $SETTINGS['mysql_user'],
    $SETTINGS['mysql_pass'],
    $database
);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(["error" => "Database connection failed: " . $conn->connect_error]);
    exit();
}

// --- EXECUTE QUERY ---
$query = $input['query'];
$result = $conn->query($query);
if ($result === false) {
    http_response_code(400);
    echo json_encode(["error" => "Query failed: " . $conn->error]);
    $conn->close();
    exit();
}

// --- RETURN RESULT ---
if ($result === true) {
    // For INSERT/UPDATE/DELETE
    echo json_encode(["success" => true, "affected_rows" => $conn->affected_rows]);
} else {
    // For SELECT
    $rows = [];
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
    }
    echo json_encode(["success" => true, "rows" => $rows]);
    $result->free();
}
$conn->close();
?>
