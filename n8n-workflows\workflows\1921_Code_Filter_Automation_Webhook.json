{"id": "30r9acI1XVIIwAMi", "meta": {"instanceId": "378c072a34d9e63949fd9cf26b8d28ff276a486e303f0d8963f23e1d74169c1b", "templateCredsSetupCompleted": true}, "name": "mails2notion V2", "tags": [], "nodes": [{"id": "3f649e97-e568-47ff-b175-bf63d859d95f", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2560, 240], "parameters": {"model": "gpt-4o", "options": {"temperature": 0, "responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "mrgqM64cM1L88xC6", "name": "<EMAIL>"}}, "typeVersion": 1}, {"id": "bd60c65f-ba6c-4dcb-8d09-b29f5dd475b7", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "disabled": true, "position": [2700, 240], "parameters": {}, "typeVersion": 1}, {"id": "d052786a-92a0-4f9b-9867-2dd64ada8034", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2820, 240], "parameters": {"jsonSchemaExample": "{\n \"summary\": \"Text\",\n \"meta\": {\n \"sender\": \"Text\",\n \"subject\": \"Text\",\n \"date\": \"Text\"\n }\n}"}, "typeVersion": 1.2}, {"id": "50d396fd-d3b0-4fea-99d7-18bd4773cb20", "name": "Add Label \"Processed\"", "type": "n8n-nodes-base.gmail", "position": [3860, 20], "parameters": {"labelIds": "={{ $('Globals').item.json.processedLabelID }}", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 2.1}, {"id": "8a4c49f9-0c14-46ea-a475-a0d83eb9d688", "name": "Active Routes Only", "type": "n8n-nodes-base.filter", "position": [2000, 20], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "02b11920-e737-46cc-b1b9-22ffaf7f3f64", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.Active }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "fd0f902f-4d16-4bad-8ed0-7fe02e8e879b", "name": "Extract Route ID", "type": "n8n-nodes-base.set", "position": [1560, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "acfaf63a-74de-4018-ae30-671f209878ba", "name": "route", "type": "string", "value": "={{ $('Gmail Trigger').item.json.to.text.match(/\\+([^@]+)@/)[1] }}"}]}}, "typeVersion": 3.4}, {"id": "81d1dec6-aacc-480d-8cb4-1832ff27de92", "name": "Deactivate Route", "type": "n8n-nodes-base.airtable", "position": [3420, 220], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appuqZhHVVGAcMwoA", "cachedResultUrl": "https://airtable.com/appuqZhHVVGAcMwoA", "cachedResultName": "mails2notion"}, "table": {"__rl": true, "mode": "list", "value": "tblWL6FqfLkLHmLEo", "cachedResultUrl": "https://airtable.com/appuqZhHVVGAcMwoA/tblWL6FqfLkLHmLEo", "cachedResultName": "Routes"}, "columns": {"value": {"id": "={{ $('Get Route by ID').item.json.id }}", "Active": false}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Token", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Token", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "NotionDatabase", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "NotionDatabase", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "User", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "User", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Active", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Active", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "kHzLZhbAFQ1CQnQz", "name": "Airtable (octionicsolutions)"}}, "typeVersion": 2.1}, {"id": "20242505-c57e-424c-a215-2b2effac1d94", "name": "Add Label \"Error\"", "type": "n8n-nodes-base.gmail", "position": [3860, 220], "parameters": {"labelIds": "={{ $('Globals').item.json.errorLabelID }}", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "operation": "addLabels"}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 2.1}, {"id": "7a788a4f-f0a8-4fe8-b21d-b114a65313b1", "name": "Send notification about deactivated route", "type": "n8n-nodes-base.gmail", "position": [3640, 220], "parameters": {"sendTo": "={{ $('Gmail Trigger').item.json.from.value[0].address }}", "message": "=An error happened while trying to create a Notion Page. It can have various reasons, including a temporary outage of the Notion API, missing permissions to the Notion Database or a wrong Notion Database URL.\n\nThe route has been deaktivated to prevent future errors.\n\nPlease double check your configuration and enable the route again.", "options": {"appendAttribution": false}, "subject": "A route has been deactivated", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 2.1}, {"id": "5e7cc69c-8f58-4ac8-9263-1ad206609295", "name": "Send notification about missing route", "type": "n8n-nodes-base.gmail", "position": [3640, 420], "parameters": {"sendTo": "={{ $('Gmail Trigger').item.json.from.value[0].address }}", "message": "=There seems to be no active route anymore which connects this Alias to a Notion Database.\n\nPlease try again later or double check your configuration.", "options": {"appendAttribution": false}, "subject": "Your Message could not be processed", "emailType": "text"}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 2.1}, {"id": "7dd9646c-3172-4b53-82c8-4df7fd5f53ea", "name": "Get Route by ID", "type": "n8n-nodes-base.airtable", "onError": "continueErrorOutput", "position": [1780, 220], "parameters": {"id": "={{ $json.route }}", "base": {"__rl": true, "mode": "list", "value": "appuqZhHVVGAcMwoA", "cachedResultUrl": "https://airtable.com/appuqZhHVVGAcMwoA", "cachedResultName": "mails2notion"}, "table": {"__rl": true, "mode": "list", "value": "tblWL6FqfLkLHmLEo", "cachedResultUrl": "https://airtable.com/appuqZhHVVGAcMwoA/tblWL6FqfLkLHmLEo", "cachedResultName": "Routes"}, "options": {}, "operation": "get"}, "credentials": {"airtableTokenApi": {"id": "kHzLZhbAFQ1CQnQz", "name": "Airtable (octionicsolutions)"}}, "retryOnFail": true, "typeVersion": 2.1, "waitBetweenTries": 5000}, {"id": "8ddfe273-3fda-4b71-a972-5001d4fa71c1", "name": "Create Notion Page", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [3200, 20], "parameters": {"url": "https://api.notion.com/v1/pages", "method": "POST", "options": {}, "jsonBody": "={{ $json.toJsonString() }}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Get Route by ID').item.json.Token }}"}, {"name": "Notion-Version", "value": "2022-06-28"}]}}, "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 5000}, {"id": "f773e41f-13b7-483a-9886-90a4425a7f6a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [900, 220], "parameters": {"simple": false, "filters": {"labelIds": "=INBOX"}, "options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 1.1}, {"id": "918ce27c-2886-4793-81f5-e459f3299bb1", "name": "Filter for unprocessed mails", "type": "n8n-nodes-base.filter", "position": [1340, 220], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "28879541-2e66-4a31-b25f-f0419ae45f47", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $('Gmail Trigger').item.json.labelIds }}", "rightValue": "={{ $json.errorLabelID }}"}, {"id": "259a783f-5954-467b-ad52-c1e0072c2239", "operator": {"type": "array", "operation": "notContains", "rightType": "any"}, "leftValue": "={{ $('Gmail Trigger').item.json.labelIds }}", "rightValue": "={{ $json.processedLabelID }}"}, {"id": "81ef1ac2-449e-44c2-a94b-2fc9b08ec934", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $('Gmail Trigger').item.json.to.text.match(/\\+([^@]+)@/)[1] }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "14764527-ca40-4937-baa2-368b716c6f58", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [920, 600], "parameters": {}, "typeVersion": 1}, {"id": "5f955606-4063-4683-b242-2fc0a4fbf34a", "name": "Required labels", "type": "n8n-nodes-base.filter", "position": [1360, 600], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "9bb51a86-76d3-42f7-8362-1931244f8cd9", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.name }}", "rightValue": "Error"}, {"id": "28b3afb4-d727-4306-9e45-321c9bd688e3", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.name }}", "rightValue": "Processed"}]}}, "typeVersion": 2}, {"id": "697198d3-2fc2-4665-86a8-4bc16dbc3d43", "name": "Globals", "type": "n8n-nodes-base.set", "position": [1120, 220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0dcfba61-ddb5-425d-a803-f88cf36d81d9", "name": "errorLabelID", "type": "string", "value": "Label_4248329647975725750"}, {"id": "b1505eaa-1d7e-49d7-be2e-cd71f5ec2632", "name": "processedLabelID", "type": "string", "value": "Label_6498950601707174088"}]}}, "typeVersion": 3.4}, {"id": "b7efe665-97d8-4a82-a3f5-e15bffd68752", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [840, 420], "parameters": {"color": 5, "width": 742.4418604651174, "height": 361.9189248985609, "content": "## Setup\n- Disable the Gmail Trigger and enable the manual trigger here\n- Execute the workflow once\n- Copy the Gmail Label IDs from the output of the \"Required labels\" node to the \"Globals\" node\n- Disable the manual trigger here and and enable the Gmail Trigger again\n- Activate the workflow, so it runs automatically in the background\n"}, "typeVersion": 1}, {"id": "3d035d35-3760-4393-8796-cb713338c9d7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1060, 60], "parameters": {"width": 215.20930232558143, "height": 323.99999999999943, "content": "## Set Globals\nUse the setup instructions below to retrieve the values for both `errorLabelID` and `processedLabelID`"}, "typeVersion": 1}, {"id": "b420310e-c0d5-4168-94ad-4c5973dfb3ab", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, 60], "parameters": {"width": 215.49263552738452, "height": 324.4244486294891, "content": "## Select Base\nSelect the database and the table where the \"Routes\" are defined"}, "typeVersion": 1}, {"id": "c917a3cb-d745-4f37-bd8f-0350c5aef473", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [840, 140], "parameters": {"color": 7, "width": 216.47293010628914, "height": 245.005504426549, "content": "The Gmail inbox is checked every minute for new entries"}, "typeVersion": 1}, {"id": "9298ad5b-ae09-44c6-8da4-2d2bd473c3ea", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1500, 140], "parameters": {"color": 7, "width": 216.47293010628914, "height": 245.005504426549, "content": "Extract the Airtable Row ID from the Email address"}, "typeVersion": 1}, {"id": "654bbfbe-3e0f-40e0-a686-5081069d825e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1280, 140], "parameters": {"color": 7, "width": 216.47293010628914, "height": 245.005504426549, "content": "Filter by labels to prohibit double-processing"}, "typeVersion": 1}, {"id": "31ade897-22de-4b39-8f96-37bc7b274bfb", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2920, -120], "parameters": {"color": 7, "width": 216.47293010628914, "height": 305.2192252594149, "content": "Dynamically build request body for Notion, since dynamic auth, and content with optional fields require a custom request"}, "typeVersion": 1}, {"id": "26cf52ea-01d1-48ed-9d3d-71e4ff01983f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3140, -120], "parameters": {"color": 7, "width": 216.47293010628914, "height": 304.5973623748489, "content": "The custom built request including the user specific authentication is sent to Notion to create a new Page inside of a database"}, "typeVersion": 1}, {"id": "d765c84d-9e15-44c8-b975-2c366c315bfe", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2160, -160], "parameters": {"color": 7, "width": 755.8332895195936, "height": 529.1698390841688, "content": "The Email is processed in multiple ways:\n- An actionable task is being generated based on the content, consisting of a short title, a short description and optionally a few details as bullet points\n- A detailed Email summary is being generated\n- Meta data is being extracted - so the user has a reference to find the original Email again\n- To get more stable results, the tasks are devided between two Agents"}, "typeVersion": 1}, {"id": "0103f8bc-2a43-455a-88da-b7317821f0b3", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1940, -80], "parameters": {"color": 7, "width": 216.47293010628914, "height": 249.09934448053562, "content": "Skip disabled routes (determined by a checkbox attribute in Airtable)"}, "typeVersion": 1}, {"id": "1d2fe867-f3d1-4702-b35e-f730f20b7251", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [2000, 420], "parameters": {}, "typeVersion": 1}, {"id": "758d1797-0e6c-40de-a6a4-e16f8350674c", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [3580, 100], "parameters": {"color": 7, "width": 216.47293010628914, "height": 503.00412949500975, "content": "Send custom Email notifications back to sender, containing an error message and suggestions to fix it"}, "typeVersion": 1}, {"id": "56522a6d-c961-48a5-a5ef-33df96d77a22", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [3800, -60], "parameters": {"color": 7, "width": 216.47293010628914, "height": 446.3164817463921, "content": "Add labels which prevent from double-processing"}, "typeVersion": 1}, {"id": "5b81389b-49a6-4849-becf-35c4e680b734", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [3360, 120], "parameters": {"color": 7, "width": 216.47293010628914, "height": 261.3816681594028, "content": "Disable a checkbox attribute in Airtable which determines if a route is active"}, "typeVersion": 1}, {"id": "6558328c-30cf-4f37-a0cb-d5f9f6efa7b2", "name": "Format Notion Page Blocks", "type": "n8n-nodes-base.code", "position": [2980, 20], "parameters": {"mode": "runOnceForEachItem", "jsCode": "function paragraph(content, annotations={}) {\n return {\n \"object\": \"block\",\n \"type\": \"paragraph\",\n \"paragraph\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": content\n },\n \"annotations\": annotations\n }\n ]\n }\n };\n}\nfunction bulletPoint(content) {\n return {\n \"object\": \"block\",\n \"type\": \"bulleted_list_item\",\n \"bulleted_list_item\": {\n \"rich_text\": [\n {\n \"type\": \"text\",\n \"text\": {\n \"content\": content\n }\n }\n ]\n }\n };\n}\n\n// combine AI generated content\nconst content = Object.assign({}, $('Generate Actionable Task').item.json.output, $('Get Summary & Meta Data').item.json.output);\n\nblocks = [];\n\n// append task description\nblocks.push(paragraph(content.description));\n\nif (content.bulletpoints) {\n for (let bulletpoint of content.bulletpoints) {\n blocks.push(bulletPoint(bulletpoint));\n }\n}\n\n// append empty line\nblocks.push(paragraph(\"\"));\n\n// append devider\nblocks.push({\n \"object\": \"block\",\n \"type\": \"divider\",\n \"divider\": {}\n});\n\n// append summary & meta data\nblocks.push(paragraph(\"Email summary:\"));\nblocks.push(paragraph(content.summary));\nblocks.push(paragraph(\"\"));\nblocks.push(paragraph(content.meta.sender + \"\\n\" + content.meta.subject + \"\\n\" + content.meta.date, {\"italic\": true}));\n\n// build final object\noutput = {\n \"parent\": {\n \"database_id\": $('Get Route by ID').item.json.NotionDatabase.match(/https:\\/\\/www\\.notion\\.so\\/[a-zA-Z0-9-]+\\/([a-zA-Z0-9]{32})/)[1]\n },\n \"properties\": {\n \"Name\": {\n \"title\": [\n {\n \"text\": {\n \"content\": content.title\n }\n }\n ]\n }\n },\n \"children\": blocks\n};\n\nreturn { json: output };"}, "typeVersion": 2}, {"id": "133e3498-10ce-4a08-aa50-3c7d56f1b9c8", "name": "Get all labels", "type": "n8n-nodes-base.gmail", "position": [1140, 600], "parameters": {"resource": "label", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "9LLNsPzyDJlQFgdw", "name": "Gmail (mails2notion)"}}, "typeVersion": 2.1}, {"id": "f68e66e1-9f84-498a-bfc4-f7c5b2ca42b1", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2440, 240], "parameters": {"jsonSchemaExample": "{\n \"title\": \"Title\",\n \"description\": \"Text\",\n \"bulletpoints\": [\n \"Text\",\n \"Text\"\n ]\n}"}, "typeVersion": 1.2}, {"id": "c55a3e9b-5637-4775-a0a6-ea11f1bd26a7", "name": "Calculator1", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "disabled": true, "position": [2320, 240], "parameters": {}, "typeVersion": 1}, {"id": "4d4f7b04-5431-47d2-b9b1-ee2c516e729c", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2180, 240], "parameters": {"model": "gpt-4o", "options": {"temperature": 0, "responseFormat": "json_object"}}, "credentials": {"openAiApi": {"id": "mrgqM64cM1L88xC6", "name": "<EMAIL>"}}, "typeVersion": 1}, {"id": "ea081c31-2721-4e6c-820a-2f0da33495ac", "name": "Generate Actionable Task", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2220, 20], "parameters": {"text": "={{ $('G<PERSON> Trigger').item.json.text }}", "options": {"systemMessage": "Your task is to understand the Email content and extract one actionable task. If there is no obvious actionable task, then just create a title which implies to take a look at this Email by addressing the content summarized to 5 words. The title should be quite decided. This attribute is called title.\n\nCreate a proper description for the task. Be precise but detailed. Start with a short sentence and if it is worth adding more information, add bulletpoints after that containing additional information which help to understand the context of the task better, like links and other references, or just more detailed instructions. Add the description to the output as attribute output. Add the bulletpoints to the output as attribute output, but remember, bullet points are optional.\n\nReturn all attributes in a JSON format."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "6fb2d964-dc0b-45d9-8307-6da16fba769e", "name": "Get Summary & Meta Data", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2600, 20], "parameters": {"text": "={{ $('G<PERSON> Trigger').item.json.text }}", "options": {"systemMessage": "Summarize the email (as much detail as possible) and add it to the output as the attribute summary.\n\nExtract the email sender, subject and date of receipt. If this is a forwarded email, then get this data from the original message, otherwise use the meta data of this Email. Format the Email Adress as follows, and add it to the JSON output as the attribute meta.sender: \"From: Full Name <<EMAIL>\". Format the the subject as follows and add it to the output as attribute meta.subject: \"Subject: SubjectGoesHere\". Format the the date as follows and add it to the output as attribute meta.date: \"Date: DateStringGoesHere\" (Date format: RFC 2822).\n\nReturn all attributes in a JSON format."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ee560597-bc46-4255-89b9-af8fe332b226", "connections": {"Globals": {"main": [[{"node": "Filter for unprocessed mails", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Get Summary & Meta Data", "type": "ai_tool", "index": 0}]]}, "Calculator1": {"ai_tool": [[{"node": "Generate Actionable Task", "type": "ai_tool", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}, "Get all labels": {"main": [[{"node": "Required labels", "type": "main", "index": 0}]]}, "Get Route by ID": {"main": [[{"node": "Active Routes Only", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Deactivate Route": {"main": [[{"node": "Send notification about deactivated route", "type": "main", "index": 0}]]}, "Extract Route ID": {"main": [[{"node": "Get Route by ID", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Get Summary & Meta Data", "type": "ai_languageModel", "index": 0}]]}, "Active Routes Only": {"main": [[{"node": "Generate Actionable Task", "type": "main", "index": 0}]]}, "Create Notion Page": {"main": [[{"node": "Add Label \"Processed\"", "type": "main", "index": 0}], [{"node": "Deactivate Route", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Generate Actionable Task", "type": "ai_languageModel", "index": 0}]]}, "Get Summary & Meta Data": {"main": [[{"node": "Format Notion Page Blocks", "type": "main", "index": 0}]]}, "Generate Actionable Task": {"main": [[{"node": "Get Summary & Meta Data", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "Send notification about missing route", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Get Summary & Meta Data", "type": "ai_outputParser", "index": 0}]]}, "Format Notion Page Blocks": {"main": [[{"node": "Create Notion Page", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Generate Actionable Task", "type": "ai_outputParser", "index": 0}]]}, "Filter for unprocessed mails": {"main": [[{"node": "Extract Route ID", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get all labels", "type": "main", "index": 0}]]}, "Send notification about missing route": {"main": [[{"node": "Add Label \"Error\"", "type": "main", "index": 0}]]}, "Send notification about deactivated route": {"main": [[{"node": "Add Label \"Error\"", "type": "main", "index": 0}]]}}}