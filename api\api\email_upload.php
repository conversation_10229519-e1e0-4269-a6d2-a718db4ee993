<?php
$uploadDir = __DIR__ . '/email_files/';
$baseUrl = (isset($_SERVER['HTTPS']) ? "https" : "http") . "://$_SERVER[HTTP_HOST]/api/email_files/";

// Create folder if not exists
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    $originalName = basename($_FILES['file']['name']);
    
    // Optional: remove dangerous characters except Arabic, letters, numbers, dots, dashes
    $safeName = preg_replace('/[^\p{Arabic}a-zA-Z0-9\.\-\_]/u', '_', $originalName);

    $destination = $uploadDir . $safeName;

    if (move_uploaded_file($_FILES['file']['tmp_name'], $destination)) {
        // Encode for URL
        $encodedName = rawurlencode($safeName);
        echo json_encode([
            'success' => true,
            'original' => $originalName,
            'url' => $baseUrl . $encodedName
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to move uploaded file.']);
    }
} else {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No file uploaded.']);
}
