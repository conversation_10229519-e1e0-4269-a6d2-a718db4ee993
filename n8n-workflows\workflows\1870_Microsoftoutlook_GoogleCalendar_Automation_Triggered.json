{"id": "0HVA2TOmkdNpH5DP", "meta": {"instanceId": "ba8f1362d8ed4c2ce84171d2f481098de4ee775241bdc1660d1dce80434ec7d4", "templateCredsSetupCompleted": true}, "name": "Google calendar to Outlook", "tags": [], "nodes": [{"id": "e7e75d4a-ee5a-4ee7-b69d-71d8eb51fe55", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [920, 800], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineAll"}, "typeVersion": 3}, {"id": "6e159340-910c-4c1e-9e6b-c6ef679309be", "name": "Incoming Event Trigger", "type": "n8n-nodes-base.googleCalendarTrigger", "position": [500, 360], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "eventCreated", "calendarId": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "Your Name"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "IgBZqXCtaacRpIKt", "name": "Your Name Google Calendar account"}}, "typeVersion": 1}, {"id": "7ffb13c3-7d16-4bd8-aed0-7f6378394a1c", "name": "Cancel Event Trigger", "type": "n8n-nodes-base.googleCalendarTrigger", "position": [280, 600], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "eventCancelled", "calendarId": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "Your Name"}}, "credentials": {"googleCalendarOAuth2Api": {"id": "IgBZqXCtaacRpIKt", "name": "Your Name Google Calendar account"}}, "typeVersion": 1}, {"id": "f0e81f5b-a813-4e03-9400-a97842b6b9b5", "name": "Create Outlook Event", "type": "n8n-nodes-base.microsoftOutlook", "position": [740, 360], "parameters": {"subject": "={{ \"From private: \" + $json.summary }}", "resource": "event", "operation": "create", "calendarId": {"__rl": true, "mode": "list", "value": "AAMkAGUxOTQ4ZmU0LWMxYjUtNDRiZi1iYjdlLTNmYTFhOWQ3MWZhNwBGAAAAAABlzj22ZOwJQZOQBjwNTK5fBwBW9yW5dIfsR51ayk6B4bZSAAAAAAEGAABW9yW5dIfsR51ayk6B4bZSAAAAAeGaAAA=", "cachedResultName": "Calendar"}, "endDateTime": "={{ $json.end.dateTime != undefined ? $json.end.dateTime : $json.end.date }}", "startDateTime": "={{ $json.start.dateTime != undefined ? $json.start.dateTime : $json.start.date }}", "additionalFields": {"body": "={{ $json.description != undefined ? $json.description + \"\\n\" : \"\" + $json.htmlLink }}"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "IsGdpQBgFdZ9bMsM", "name": "Microsoft Outlook account (alex NLD)"}}, "typeVersion": 2}, {"id": "0e7c3511-cb4a-46a7-937e-57bdf6bdc00c", "name": "Get Event to Cancel", "type": "n8n-nodes-base.microsoftOutlook", "position": [520, 600], "parameters": {"limit": 1, "filters": {"custom": "=contains(subject, '{{ $json.summary }}')"}, "resource": "event", "calendarId": {"__rl": true, "mode": "list", "value": "AAMkAGUxOTQ4ZmU0LWMxYjUtNDRiZi1iYjdlLTNmYTFhOWQ3MWZhNwBGAAAAAABlzj22ZOwJQZOQBjwNTK5fBwBW9yW5dIfsR51ayk6B4bZSAAAAAAEGAABW9yW5dIfsR51ayk6B4bZSAAAAAeGaAAA=", "cachedResultName": "Calendar"}, "fromAllCalendars": false}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "IsGdpQBgFdZ9bMsM", "name": "Microsoft Outlook account (work email)"}}, "typeVersion": 2}, {"id": "6540c5f5-963b-4260-8c10-1c7f5bb75315", "name": "Delete Event", "type": "n8n-nodes-base.microsoftOutlook", "position": [780, 600], "parameters": {"eventId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "resource": "event", "operation": "delete", "calendarId": {"__rl": true, "mode": "list", "value": "AAMkAGUxOTQ4ZmU0LWMxYjUtNDRiZi1iYjdlLTNmYTFhOWQ3MWZhNwBGAAAAAABlzj22ZOwJQZOQBjwNTK5fBwBW9yW5dIfsR51ayk6B4bZSAAAAAAEGAABW9yW5dIfsR51ayk6B4bZSAAAAAeGaAAA=", "cachedResultName": "Calendar"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "IsGdpQBgFdZ9bMsM", "name": "Microsoft Outlook account (alex NLD)"}}, "typeVersion": 2}, {"id": "03cf261c-4c26-4db1-a335-e249c0f590ec", "name": "Send E-mail with details", "type": "n8n-nodes-base.microsoftOutlook", "position": [1060, 620], "parameters": {"subject": "={{ $json.subject + \" Cancelled\" }}", "bodyContent": "<h1>Event cancelled via Google Calendar</h1>", "toRecipients": "<EMAIL>", "additionalFields": {"bodyContentType": "html"}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "IsGdpQBgFdZ9bMsM", "name": "Microsoft Outlook account (work email)"}}, "typeVersion": 2}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "34dc3a4d-0db5-4efc-8814-c94d3468540a", "connections": {"Merge": {"main": [[{"node": "Send E-mail with details", "type": "main", "index": 0}]]}, "Delete Event": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get Event to Cancel": {"main": [[{"node": "Delete Event", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Cancel Event Trigger": {"main": [[{"node": "Get Event to Cancel", "type": "main", "index": 0}]]}, "Incoming Event Trigger": {"main": [[{"node": "Create Outlook Event", "type": "main", "index": 0}]]}}}