<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

if (!empty($_FILES)) {
    echo "<pre>_FILES:\n";
    print_r($_FILES);
    echo "</pre>";
}

if (!empty($_POST)) {
    echo "<pre>_POST:\n";
    print_r($_POST);
    echo "</pre>";
}

if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
    echo "File upload was successful.";
} elseif (isset($_FILES['file'])) {
    echo "File upload failed with error code: " . $_FILES['file']['error'];
} else {
    echo "No file was uploaded.";
}
?>