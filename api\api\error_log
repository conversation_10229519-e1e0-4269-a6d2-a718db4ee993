[08-May-2025 03:03:28 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Table 'icredept_tracking.tracking' doesn't exist in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT * FROM t...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[08-May-2025 04:18:12 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Table 'icredept_1comm.Tracking' doesn't exist in /home2/icredept/public_html/api/api.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api.php(66): mysqli->query('select * from T...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api.php on line 66
[08-May-2025 06:10:34 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Access denied for user 'icredept_tariq'@'%' to database 'tracking' in /home2/icredept/public_html/api/api_tracking.php:52
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(52): mysqli->__construct('162.144.3.34', 'icredept_tariq', Object(SensitiveParameterValue), 'tracking')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 52
[08-May-2025 06:21:11 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'message_id' in 'field list' in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT message_...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[08-May-2025 06:21:44 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'message_id' in 'field list' in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT message_...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[15-May-2025 17:01:42 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'day').format('YYYY-MM-DD')}}' AND t.ApplicationNumber NOT IN (SELECT DISTINCT Ap' at line 1 in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT COUNT(DI...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[15-May-2025 17:01:52 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'day').format('YYYY-MM-DD')}}' AND t.ApplicationNumber NOT IN (SELECT DISTINCT Ap' at line 1 in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT COUNT(DI...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[15-May-2025 17:02:12 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1 in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('SELECT COUNT(DI...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[16-May-2025 16:59:36 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Table 'icredept_tracking.modelsUsed' doesn't exist in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('INSERT INTO mod...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[16-May-2025 17:54:56 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '? 0 : 1 + 1)' at line 1 in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('INSERT INTO mod...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
[06-Jun-2025 09:33:44 America/Chicago] PHP Fatal error:  Uncaught mysqli_sql_exception: Column count doesn't match value count at row 1 in /home2/icredept/public_html/api/api_tracking.php:66
Stack trace:
#0 /home2/icredept/public_html/api/api_tracking.php(66): mysqli->query('INSERT INTO mod...')
#1 {main}
  thrown in /home2/icredept/public_html/api/api_tracking.php on line 66
