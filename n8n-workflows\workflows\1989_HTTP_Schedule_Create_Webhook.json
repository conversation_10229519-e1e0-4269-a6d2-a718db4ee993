{"id": "v9K61fCQhrG6gt6Z", "meta": {"instanceId": "9219ebc7795bea866f70aa3d977d54417fdf06c41944be95e20cfb60f992db19", "templateCredsSetupCompleted": true}, "name": "Search news using Perplexity AI and post to X (Twitter)", "tags": [], "nodes": [{"id": "9b2fdc0f-8c71-4ea9-a9d0-df470f2778df", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-560, 0], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 21, "triggerAtMinute": "={{Math.floor(Math.random() * 60)}}\n"}]}}, "typeVersion": 1.2}, {"id": "d549c019-1f3e-4758-a5ee-e4ac7e03cc2d", "name": "searchQuery", "type": "n8n-nodes-base.set", "position": [-340, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4cfbc312-5dcd-444d-ae08-0bab417c084c", "name": "searchInput", "type": "string", "value": "What's the latest news in artificial intelligence?"}]}}, "typeVersion": 3.4}, {"id": "87c28d43-6cd6-4292-a563-a9f22467e162", "name": "Perplexity", "type": "n8n-nodes-base.httpRequest", "position": [100, 0], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n  \"model\": \"llama-3.1-sonar-small-128k-online\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a social media assistant summarizing tech news for Twitter/X. Only return one article. Your output must follow this exact format: a short, engaging headline (max 140 characters), followed by a single space, then the direct article link. Do not use markdown, hashtags, emojis, or line breaks. Keep the total output under 200 characters. Be precise, objective, and newsworthy.Example: Mastercard launches Agent Pay, allowing AI agents to make purchases for users. https://www.perplexity.ai/page/mastercard-unveils-agent-pay-e-qWXnaUEzQZWCqsxF4l43zA\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $('searchQuery').item.json.searchInput }}\"\n    }\n  ],\n  \"temperature\": 0.3,\n  \"top_p\": 0.9,\n  \"return_citations\": true,\n  \"search_domain_filter\": [\n    \"perplexity.ai\"\n  ],\n  \"search_recency_filter\": \"day\",\n  \"return_images\": true,\n  \"return_related_questions\": false,\n  \"max_tokens\": 80,\n  \"stream\": false,\n  \"presence_penalty\": 0,\n  \"frequency_penalty\": 1\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $json.perplexityAPI }}"}]}}, "typeVersion": 4.2}, {"id": "c1ed633d-d318-403c-9577-c3c63ac2e68e", "name": "set API key", "type": "n8n-nodes-base.set", "position": [-120, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4f9bd3a0-5587-410f-b145-a287f65f9576", "name": "perplexityAPI", "type": "string", "value": "<yourPerplexityAPI>"}]}}, "typeVersion": 3.4}, {"id": "e228e352-2ddd-4e2c-a434-993910ced7be", "name": "Post to X", "type": "n8n-nodes-base.twitter", "position": [320, 0], "parameters": {"text": "={{ $json.choices[0].message.content }}", "additionalFields": {}}, "credentials": {"twitterOAuth2Api": {"id": "NY8wGzcN4f9f1UN4", "name": "X account 2 for images"}}, "typeVersion": 2}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "qFOYM3IA9QZ7fMym", "executionOrder": "v1"}, "versionId": "74e316fe-561f-4c80-b446-bba795654cef", "connections": {"Post to X": {"main": [[]]}, "Perplexity": {"main": [[{"node": "Post to X", "type": "main", "index": 0}]]}, "searchQuery": {"main": [[{"node": "set API key", "type": "main", "index": 0}]]}, "set API key": {"main": [[{"node": "Perplexity", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "searchQuery", "type": "main", "index": 0}]]}}}