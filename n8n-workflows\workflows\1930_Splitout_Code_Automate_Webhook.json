{"id": "nGpVbW7RTylKujyT", "meta": {"instanceId": "dcb7e9805ce8fe33e4ef843b02947aacc9de2ca8e3594435f3a36d9f33df54fc", "templateCredsSetupCompleted": true}, "name": "AI powered SEO Keyword Research Automation - The vibe Marketer", "tags": [{"id": "SRzFKUr6fVtmWq2d", "name": "works", "createdAt": "2025-04-14T11:05:17.062Z", "updatedAt": "2025-04-14T11:05:17.062Z"}], "nodes": [{"id": "65aacfa5-4891-49f9-a614-2866c96142ee", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [88, 455], "parameters": {"model": {"__rl": true, "mode": "list", "value": "o1", "cachedResultName": "o1"}, "options": {}}, "credentials": {"openAiApi": {"id": "AZynAxNG099jyj7B", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "5a0445ad-20c9-4e62-8e04-62451d3e8f7e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [208, 455], "parameters": {"jsonSchemaExample": "{\n  \"primary_keywords\": [\"string\"],\n  \"long_tail_keywords\": [\n    {\n      \"keyword\": \"string\",\n      \"intent\": \"string\"\n    }\n  ],\n  \"question_based_keywords\": [\"string\"],\n  \"related_topics\": [\"string\"]\n}\n"}, "typeVersion": 1.2}, {"id": "38cd2c66-d4b7-47a9-a3eb-f58eb32f55ab", "name": "Topic Expansion", "type": "@n8n/n8n-nodes-langchain.agent", "position": [60, 235], "parameters": {"text": "=I need to create comprehensive SEO keyword research for content about: \n{{ $json.primary_topic }}\n\nMy target audience is: {{ $json.target_audience }}\nThis will be used for a: {{ $json.content_type }}\nLocation: {{ $json.location }}\nLanguage: {{ $json.language }}\n\nPlease generate:\n1. A list of 20 primary keywords directly related to {{ $json.primary_topic }}\n2. 30 long-tail keyword variations with search intent (informational, commercial, transactional)\n3. 15 question-based keywords people might ask about this topic\n4. 10 related topics that could be used for supporting content\n\nFormat the output as a structured JSON with these categories. ", "options": {}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.8}, {"id": "14811f51-5992-4e35-af8d-f05f5b488bc1", "name": "Competitor Analysis", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1100, 660], "parameters": {"text": "=Analyze the following competitor content for the Primary Topic \"{{ $('Format Json and add Competitor URLs').item.json.primary_topic }}\":\n\nCompetitor: {{ $('Split the Competitor URLs').item.json.competitorUrls }}\nDATA: ```\n{{ $json.tasks[0].result.toJsonString() }}\n```\n\nPlease identify:\n1. Primary keywords they appear to be targeting\n2. Content gaps or missing topics they aren't covering\n3. Unique angles or approaches they're taking\n4. Questions they're answering (or not answering)\n\nFormat the output as a structured analysis. ", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "564866ac-c287-48a3-816c-78207dfce133", "name": "Keyword Difficulty", "type": "n8n-nodes-dataforseo.dataForSeo", "position": [656, 260], "parameters": {"keywords": {"values": [{"value": "={{ $json['output.primary_keywords'] }}"}]}, "resource": "labs", "operation": "get-keyword-difficulty", "language_name_required": "={{ $('Set relevant fields').item.json.language }}", "location_name_required": "={{ $('Set relevant fields').item.json.location }}"}, "credentials": {"dataForSeoApi": {"id": "owHrK02rkWLlYrl3", "name": "DataForSEO account"}}, "typeVersion": 1}, {"id": "d39e392c-c547-4edd-8fe9-014c26152915", "name": "Search Volume & CPC", "type": "n8n-nodes-dataforseo.dataForSeo", "position": [656, 60], "parameters": {"date_to": {}, "keywords": {"values": [{"value": "={{ $json['output.primary_keywords'] }}"}]}, "resource": "keywords_data", "date_from": {}, "language_name": "={{ $('Set relevant fields').item.json.language }}", "location_name": "={{ $('Set relevant fields').item.json.location }}"}, "credentials": {"dataForSeoApi": {"id": "owHrK02rkWLlYrl3", "name": "DataForSEO account"}}, "typeVersion": 1}, {"id": "2985d85c-4373-4f18-9c27-188f19c920a6", "name": "split primary keywords", "type": "n8n-nodes-base.splitOut", "position": [440, 160], "parameters": {"options": {}, "fieldToSplitOut": "output.primary_keywords"}, "typeVersion": 1}, {"id": "********-744d-45a5-b0ed-b74a1a57aad8", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1920, 640], "parameters": {"model": {"__rl": true, "mode": "list", "value": "o1", "cachedResultName": "o1"}, "options": {}}, "credentials": {"openAiApi": {"id": "AZynAxNG099jyj7B", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "********-14b1-402a-a084-50fd775d6523", "name": "Keyword Ranking per URL", "type": "n8n-nodes-dataforseo.dataForSeo", "position": [880, 660], "parameters": {"limit": 10, "target": "={{ $json.competitorUrls }}", "resource": "labs", "operation": "get-ranked-keywords", "language_name_required": "={{ $('Format Json and add Competitor URLs').item.json.language }}", "location_name_required": "={{ $('Format Json and add Competitor URLs').item.json.location }}"}, "credentials": {"dataForSeoApi": {"id": "owHrK02rkWLlYrl3", "name": "DataForSEO account"}}, "typeVersion": 1}, {"id": "1e1f4a81-31b9-450d-85ba-65dbe2b6e8c2", "name": "Final Keyword Strategy", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1940, 440], "parameters": {"text": "=# Role: Act as an expert SEO Strategist and Content Planner.\n\n# Context:\n# You are creating an actionable SEO Keyword Strategy & Content Brief based on prior AI-driven keyword generation and competitor analysis.\n# The goal is content creation for the 'Primary Topic', targeting the specified 'Target Audience' and 'Content Type' in the given 'Location' and 'Language'.\n# Data provided includes initial keyword ideas (primary, long-tail, questions), keyword metrics (volume, difficulty), related topics, and competitor analysis insights (their likely keywords, content gaps, unique angles).\n\n# Input Parameters for this Task:\nPrimary Topic: {{ $json.primary_topic }}\nTarget Audience: {{ $json.target_audience }}\nContent Type: {{ $json.content_type }}\nLocation: {{ $json.location }}\nLanguage: {{ $json.language }}\nAnalyzed Compeitors: {{ $json.competitor_urls }}\n\n# Your Task:\n# Analyze the provided input parameters and the detailed 'DATA' section below.\n# Synthesize this information into a clear, concise, and actionable SEO Keyword Strategy & Content Brief.\n# Structure the output logically using Markdown. Focus on providing insights and actionable recommendations, not just listing data. Explain the 'why' behind key recommendations. Keep the language easy to understand, assuming the reader (e.g., a content writer or marketing manager) understands basic SEO concepts but isn't necessarily a deep expert.\n\n# Required Output Sections (Use Markdown Headers):\n\n## 1. Executive Summary\n   - **Objective:** Briefly state the primary goal of creating content on this topic for this audience (e.g., \"Attract [Target Audience] seeking information on [Primary Topic]...\" or \"Position our brand as a thought leader for [Target Audience] regarding [Primary Topic]\").\n   - **Key Opportunity:** Summarize the most significant keyword opportunity identified (e.g., \"Target the high-volume term '[Example Keyword]' while capturing related informational queries via long-tail variations.\")\n   - **Competitor Angle:** Briefly mention the main strategic takeaway from the competitor analysis (e.g., \"Competitors focus heavily on [X], leaving an opportunity to address [Y] or provide a unique angle on [Z].\")\n\n## 2. Target Keyword Strategy & Rationale\n   - **Primary Target Keywords:**\n      - List the top 5-10 recommended primary keywords.\n      - For each, include Search Volume (SV) and Keyword Difficulty (KD).\n      - **Add brief commentary/rationale for each group or key term:** Why were these chosen? (e.g., \"High relevance and strong search volume despite moderate difficulty,\" or \"Balances primary topic focus with user search behavior.\")\n   - **Secondary & Long-Tail Opportunities:**\n      - List the top 10-15 recommended long-tail and secondary keywords.\n      - Group them by likely Search Intent (e.g., Informational, Commercial, Transactional) if discernible from the input data.\n      - **Add brief commentary on the overall opportunity:** What specific user needs or funnel stages do these address? Note any clusters with particularly low competition.\n   - **Key Question Keywords:**\n      - List the top 5 question-based keywords the content *must* answer.\n      - **Add brief commentary:** Why are these questions crucial for the target audience or content goals?\n\n## 3. Competitive Landscape & Content Gaps\n   - **Competitor Focus:** Briefly summarize the main keyword themes or angles competitors seem to be targeting, based on the provided analysis.\n   - **Identified Gaps/Opportunities:** Highlight 1-3 specific content gaps, under-served intents, or unique angles identified from the competitor analysis that this content piece should leverage. Be specific (e.g., \"Competitors explain 'what', but not 'how to implement',\" or \"Lack of practical examples for [Target Audience]\").\n\n## 4. Content Outline & Actionable Recommendations\n   - **Recommended Structure:** Propose a logical H2/H3 structure or outline for the content piece, designed to cover the target keywords and address user intent effectively.\n   - **Keyword Integration:** Briefly suggest how to naturally incorporate the different keyword types (primary, long-tail, questions) within the proposed structure.\n   - **Content Enhancement:** Provide 2-3 specific, actionable recommendations to make the content stand out for the target audience and potentially outperform competitors (e.g., \"Include step-by-step instructions,\" \"Add original data/charts,\" \"Feature quotes from [Target Audience Role],\" \"Create a downloadable checklist\").\n\n## 5. Proposed SEO Titles\n   - List 3-5 compelling, SEO-optimized title options for the content piece. Ensure they are relevant, incorporate keywords naturally, and entice clicks.\n\n# DATA for Analysis:\n# (Analyze the following JSON data containing keyword suggestions, metrics, and competitor analysis results)\n```json\n{{ $json.data.toJsonString() }}\n\n{{ $json.output.toJsonString() }}\n```\n\nFinal Output Format: Ensure the entire response is well-structured, clean Markdown, ready to be used as a content brief.", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "613e1d25-3e9d-4a7f-8657-392854eb00de", "name": "Get Input from NocoDB", "type": "n8n-nodes-base.webhook", "position": [-680, 340], "webhookId": "ac7e989d-6e32-4850-83c4-f10421467fb8", "parameters": {"path": "ac7e989d-6e32-4850-83c4-f10421467fb8", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "88076d36-fe04-4a7a-a176-9ba93388b089", "name": "Split the Competitor URLs", "type": "n8n-nodes-base.splitOut", "position": [580, 660], "parameters": {"options": {}, "fieldToSplitOut": "competitorUrls"}, "typeVersion": 1}, {"id": "88d8ad2f-4b66-48e3-aaf7-d6f8210f264b", "name": "Set relevant fields", "type": "n8n-nodes-base.set", "position": [-500, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "e729ab88-95f8-44c0-948c-d2476262fd17", "name": "primary_topic", "type": "string", "value": "={{ $json.body.data.rows[0]['Primary Topic'] }}"}, {"id": "1c6fbf22-fb3f-4577-b6cc-4d0672ff2046", "name": "competitor_urls", "type": "string", "value": "={{ $json.body.data.rows[0]['Competitor URLs'] }}"}, {"id": "ea8518c8-8f89-4aa5-9546-44be77deeebb", "name": "target_audience", "type": "string", "value": "={{ $json.body.data.rows[0]['Target Audience'] }}"}, {"id": "4b27d628-6cc1-4161-bb49-d39a4b1d320e", "name": "content_type", "type": "string", "value": "={{ $json.body.data.rows[0]['Content Type'] }}"}, {"id": "bb3fefe7-7eea-4a6d-b2de-307b791ff1b6", "name": "id", "type": "string", "value": "={{ $json.body.data.rows[0].Id }}"}, {"id": "09e64ce6-39de-4550-9078-fe4f233edd9a", "name": "status", "type": "string", "value": "={{ $json.body.data.rows[0].Status }}"}, {"id": "c10736b0-dece-40a7-9fb0-86b23b44e517", "name": "location", "type": "string", "value": "={{ $json.body.data.rows[0].Location }}"}, {"id": "6508a1e9-963d-4a79-bd35-f537c892e8d4", "name": "language", "type": "string", "value": "={{ $json.body.data.rows[0].Language }}"}]}}, "typeVersion": 3.4}, {"id": "1f083fb0-8b55-43f0-85de-58a81f30a9f2", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1100, 860], "parameters": {"model": {"__rl": true, "mode": "list", "value": "o1", "cachedResultName": "o1"}, "options": {}}, "credentials": {"openAiApi": {"id": "AZynAxNG099jyj7B", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "ffcc38ac-0f0a-4fc7-8e65-86950ea6a01d", "name": "Format Json and add Competitor URLs", "type": "n8n-nodes-base.code", "position": [300, 660], "parameters": {"jsCode": "const inputJson = $input.first().json;\nconst rawUrls = inputJson.competitor_urls;\n\nconst competitorUrls = rawUrls\n  .split(\",\")\n  .map(url => url.trim())\n  .filter(url => url.length > 0);\n\nconst outputJson = {\n  ...inputJson,\n  competitorUrls: competitorUrls\n};\n\nreturn [{ json: outputJson }];\n"}, "typeVersion": 2}, {"id": "cf522a25-6e62-4a34-b5dd-6684ea67e938", "name": "Aggregate SV & CPC", "type": "n8n-nodes-base.aggregate", "position": [880, 60], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "781614a6-7afd-4465-86cb-05ef781b70fe", "name": "Aggregate KWD", "type": "n8n-nodes-base.aggregate", "position": [880, 260], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "15ab5e60-1f8d-4fd5-bfd8-983a8e0861bb", "name": "Merge SV, CPC & KWD", "type": "n8n-nodes-base.merge", "position": [1174, 160], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "19b056e8-8fb3-436a-b8be-356eeedbb57e", "name": "Merge Topic Expansion, SV, CPC & KWD", "type": "n8n-nodes-base.merge", "position": [1472, 235], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3.1}, {"id": "460a5cf3-691e-44c8-a1d3-8dcd43728851", "name": "Aggregate Competitor Analysis", "type": "n8n-nodes-base.aggregate", "position": [1472, 660], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "b28fe5bb-2ca1-4c02-b45f-033af494d706", "name": "Merge <PERSON>", "type": "n8n-nodes-base.merge", "position": [1720, 440], "parameters": {"mode": "combine", "options": {"includeUnpaired": false}, "combineBy": "combineByPosition", "numberInputs": 3}, "typeVersion": 3.1}, {"id": "a4851979-f231-458b-be3f-13eb3c14b0ee", "name": "Write Content Brief ", "type": "n8n-nodes-base.nocoDb", "position": [2320, 440], "parameters": {"table": "mfsjucjn304v1hc", "fieldsUi": {"fieldValues": [{"fieldName": "primary_topic_used", "fieldValue": "={{ $('Merge Everything').item.json.primary_topic }}"}, {"fieldName": "report_content", "fieldValue": "={{ $json.output }}"}]}, "operation": "create", "projectId": "pl6znsxtne8x3yh", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "Nqxw0TptKnROWv9i", "name": "NocoDB (hosted) Token account"}}, "typeVersion": 3}, {"id": "e13ecdf4-0696-4bd7-bbf5-49cb508072c6", "name": "Update Status - Done", "type": "n8n-nodes-base.nocoDb", "position": [2320, 600], "parameters": {"table": "mp3qmbuye3pyihc", "fieldsUi": {"fieldValues": [{"fieldName": "Id", "fieldValue": "={{ $('Merge Everything').item.json.id }}"}, {"fieldName": "=Status", "fieldValue": "Done"}]}, "operation": "update", "projectId": "pl6znsxtne8x3yh", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "Nqxw0TptKnROWv9i", "name": "NocoDB (hosted) Token account"}}, "typeVersion": 3}, {"id": "c23e1c12-41d3-4c91-8175-f035024c6339", "name": "Send Notification", "type": "n8n-nodes-base.slack", "position": [2320, 800], "webhookId": "d4615307-81b9-45a3-9d03-4fe5875811c1", "parameters": {"text": "=>> DONE << \n\nSEO Keyword Research \nPrimary Topic: {{ $('Merge Everything').item.json.primary_topic }}\nTarget Audience: {{ $('Merge Everything').item.json.target_audience }}\nContent Type: {{ $('Merge Everything').item.json.content_type }}\nLocation: {{ $('Merge Everything').item.json.location }}\nLanguage: {{ $('Merge Everything').item.json.language }}\nCompetitor URLs: {{ $('Merge Everything').item.json.competitor_urls }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08Q7EQ8JNS", "cachedResultName": "seo-keyword-research"}, "otherOptions": {"mrkdwn": false, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "WSpsCFfmEwBZkHv1", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "acfee96e-7d37-4a36-b652-3b0798688538", "name": "Start Notification", "type": "n8n-nodes-base.slack", "position": [-340, 20], "webhookId": "d4615307-81b9-45a3-9d03-4fe5875811c1", "parameters": {"text": "=>> START << \n\nSEO Keyword Research \nPrimary Topic: {{ $json.primary_topic }}\nTarget Audience: {{ $json.target_audience }}\nContent Type: {{ $json.content_type }}\nLocation: {{ $json.location }}\nLanguage: {{ $json.language }}\nCompetitor URLs: {{ $json.competitor_urls }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08Q7EQ8JNS", "cachedResultName": "seo-keyword-research"}, "otherOptions": {"mrkdwn": false, "includeLinkToWorkflow": false}}, "credentials": {"slackApi": {"id": "WSpsCFfmEwBZkHv1", "name": "Slack account"}}, "typeVersion": 2.3}, {"id": "5af5855d-b858-4b9f-91bc-1cbb14c08258", "name": "Update Status - Started", "type": "n8n-nodes-base.nocoDb", "position": [-140, 40], "parameters": {"table": "mp3qmbuye3pyihc", "fieldsUi": {"fieldValues": [{"fieldName": "Id", "fieldValue": "={{ $json.id }}"}, {"fieldName": "=Status", "fieldValue": "Started"}]}, "operation": "update", "projectId": "pl6znsxtne8x3yh", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "Nqxw0TptKnROWv9i", "name": "NocoDB (hosted) Token account"}}, "typeVersion": 3}, {"id": "a9db9a15-acc1-411c-b596-810a2ce6b8f6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -140], "parameters": {"color": 7, "width": 480, "height": 360, "content": "## Notification and Update Status\n"}, "typeVersion": 1}, {"id": "bd0e0d35-dce3-47c4-bb85-de02ded10691", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [60, 40], "parameters": {"width": 280, "height": 540, "content": "## Topic Expansion"}, "typeVersion": 1}, {"id": "9bb138db-cf51-4614-aeb4-abe7b298aab7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, -80], "parameters": {"color": 5, "width": 1220, "height": 540, "content": "## Search Volume, Cost <PERSON>, Keyword Difficulty"}, "typeVersion": 1}, {"id": "757c15fc-5b5f-44d6-ae06-43dac9c32b2c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [260, 600], "parameters": {"color": 4, "width": 1360, "height": 460, "content": "## Competitor Research"}, "typeVersion": 1}, {"id": "5459b4ee-f8dd-426b-b0f2-52b8ad6e1222", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1700, 260], "parameters": {"color": 6, "width": 500, "height": 540, "content": "## Merge and write Final Keyword Strategy"}, "typeVersion": 1}, {"id": "398ff4dd-d143-4944-96f3-3284cb391d84", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2260, 260], "parameters": {"color": 7, "height": 720, "content": "## Save, Update Status and Notify"}, "typeVersion": 1}, {"id": "7d0ee538-62c7-4bdc-bd4a-be5f600c78b4", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-740, 240], "parameters": {"width": 400, "height": 320, "content": "## Input"}, "typeVersion": 1}, {"id": "42f81576-ac7c-4ab2-a93b-3c95410bd801", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [460, -280], "parameters": {"color": 3, "width": 820, "height": 80, "content": "# AI-Powered SEO Keyword Research Automation"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9abcff0c-1aff-4594-8796-8828963a3f75", "connections": {"Aggregate KWD": {"main": [[{"node": "Merge SV, CPC & KWD", "type": "main", "index": 1}]]}, "Topic Expansion": {"main": [[{"node": "split primary keywords", "type": "main", "index": 0}, {"node": "Merge Topic Expansion, SV, CPC & KWD", "type": "main", "index": 1}]]}, "Merge Everything": {"main": [[{"node": "Final Keyword Strategy", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Topic Expansion", "type": "ai_languageModel", "index": 0}]]}, "Aggregate SV & CPC": {"main": [[{"node": "Merge SV, CPC & KWD", "type": "main", "index": 0}]]}, "Keyword Difficulty": {"main": [[{"node": "Aggregate KWD", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Competitor Analysis", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Final Keyword Strategy", "type": "ai_languageModel", "index": 0}]]}, "Competitor Analysis": {"main": [[{"node": "Aggregate Competitor Analysis", "type": "main", "index": 0}]]}, "Merge SV, CPC & KWD": {"main": [[{"node": "Merge Topic Expansion, SV, CPC & KWD", "type": "main", "index": 0}]]}, "Search Volume & CPC": {"main": [[{"node": "Aggregate SV & CPC", "type": "main", "index": 0}]]}, "Set relevant fields": {"main": [[{"node": "Topic Expansion", "type": "main", "index": 0}, {"node": "Format Json and add Competitor URLs", "type": "main", "index": 0}, {"node": "Merge <PERSON>", "type": "main", "index": 2}, {"node": "Update Status - Started", "type": "main", "index": 0}, {"node": "Start Notification", "type": "main", "index": 0}]]}, "Write Content Brief ": {"main": [[]]}, "Get Input from NocoDB": {"main": [[{"node": "Set relevant fields", "type": "main", "index": 0}]]}, "Final Keyword Strategy": {"main": [[{"node": "Write Content Brief ", "type": "main", "index": 0}, {"node": "Update Status - Done", "type": "main", "index": 0}, {"node": "Send Notification", "type": "main", "index": 0}]]}, "split primary keywords": {"main": [[{"node": "Search Volume & CPC", "type": "main", "index": 0}, {"node": "Keyword Difficulty", "type": "main", "index": 0}]]}, "Keyword Ranking per URL": {"main": [[{"node": "Competitor Analysis", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Topic Expansion", "type": "ai_outputParser", "index": 0}]]}, "Split the Competitor URLs": {"main": [[{"node": "Keyword Ranking per URL", "type": "main", "index": 0}]]}, "Aggregate Competitor Analysis": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 1}]]}, "Format Json and add Competitor URLs": {"main": [[{"node": "Split the Competitor URLs", "type": "main", "index": 0}]]}, "Merge Topic Expansion, SV, CPC & KWD": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 0}]]}}}