{"id": "jOI7FRhG1FkeqBLG", "meta": {"instanceId": "2872777e468ba025c28c67ebf483f93425a37d897dfc1056e0c00cc75112d703"}, "name": "Wordpress Form to Mautic", "tags": [], "nodes": [{"id": "fcd19b7b-9104-45a6-b741-9497effbd68e", "name": "LeadData", "type": "n8n-nodes-base.set", "position": [1260, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "91215336-3a47-4e86-ac6a-1a1862b31e54", "name": "name", "type": "string", "value": "={{ $json.body.Nome.toTitleCase() }}"}, {"id": "703f1da3-3f68-4d97-94c9-c22661813d92", "name": "email", "type": "string", "value": "={{ $json.body['E-mail'].toLowerCase() }}"}, {"id": "c9ba65f1-68e9-46ed-9620-365e000aeb6c", "name": "mobile", "type": "string", "value": "={{ $json.body.WhatsApp }}"}, {"id": "3a7266cf-5ff8-4559-985d-2480d0271cbd", "name": "form", "type": "string", "value": "={{ $json.body.form_id }}"}, {"id": "06825dab-fbed-4d85-b91c-5d1c2cf8e934", "name": "email_valid", "type": "boolean", "value": "={{ $json.body['E-mail'].isEmail() }}"}]}}, "typeVersion": 3.3}, {"id": "9598d8bf-b7f0-4e5e-804c-154f240704ac", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [520, 220], "parameters": {"width": 471, "height": 370, "content": "## Receive Data from Wordpress Form\n\nYou can customize your form fields in the way that best suits your marketing campaigns."}, "typeVersion": 1}, {"id": "620d1873-3881-4086-8bd3-e26e07cab88c", "name": "WordpressForm", "type": "n8n-nodes-base.webhook", "position": [660, 420], "webhookId": "917366ee-14a8-4fef-9f0b-6638cdc35fad", "parameters": {"path": "917366ee-14a8-4fef-9f0b-6638cdc35fad", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "8f6bed52-1214-46fa-8e8a-c648bbe6e52a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1040, 220], "parameters": {"width": 551, "height": 376, "content": "## Normalize Data\n\nLet's separate the data we are going to use and remove everything that is unnecessary for the workflow. This way we avoid errors and optimize the use of N8N resources.\n\nYou can use N8N expression extensions to format and validate your data received by N8N."}, "typeVersion": 1}, {"id": "975ec9ae-d64d-42e6-b665-82296825203d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2240, 220], "parameters": {"width": 772.5, "height": 376.25, "content": "## Checks if the email can be valid\n\nChecks if the email can be valid to create the contact in Mautic with the correct registration information"}, "typeVersion": 1}, {"id": "a2f241c2-6894-4c17-a1bd-88c0c9bc88cb", "name": "CheckEmail<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.if", "position": [2420, 420], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "bcbdaa12-c4ec-4fba-85f8-ddfe5eed8f42", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $('LeadData').item.json.email_valid }}", "rightValue": "="}]}}, "typeVersion": 2}, {"id": "26a0eab3-2097-4b91-8a79-8fc2934f3ebe", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1640, 221.25], "parameters": {"width": 555, "height": 376.25, "content": "## Create Contact on <PERSON>uti<PERSON>\n\nCreate a contact in Mautic Let's create the contact in Mautic where you will map the fields you need."}, "typeVersion": 1}, {"id": "16a62af3-f9cb-4a12-b168-a2c6c5ff6c78", "name": "CreateContactMautic", "type": "n8n-nodes-base.mautic", "position": [1860, 420], "parameters": {"email": "={{ $json.email }}", "options": {}, "firstName": "={{ $json.name }}", "additionalFields": {"mobile": "={{ $json.mobile }}"}}, "credentials": {"mauticApi": {"id": "dNmbC6ievGKXw0ww", "name": "Mautic account"}}, "typeVersion": 1}, {"id": "340eb2d8-c2c0-4a31-822e-6fda2c00f4ea", "name": "LeadMauticDNC", "type": "n8n-nodes-base.mautic", "position": [2740, 380], "parameters": {"contactId": "={{ $json.id }}", "operation": "editDoNotContactList", "additionalFields": {"reason": "3", "comments": "Did not pass basic email validation"}}, "credentials": {"mauticApi": {"id": "dNmbC6ievGKXw0ww", "name": "Mautic account"}}, "typeVersion": 1}, {"id": "8b773a35-2b4b-4d50-aeed-bf5fe8e6e7d1", "name": "End", "type": "n8n-nodes-base.noOp", "position": [3140, 380], "parameters": {}, "typeVersion": 1}], "active": false, "pinData": {"WordpressForm": [{"json": {"body": {"Nome": "<PERSON><PERSON>", "E-mail": "<EMAIL>", "form_id": "1b46cae", "WhatsApp": "*************", "form_name": "Contact Form"}, "query": {}, "params": {}, "headers": {"host": "data.promovaweb.com", "accept": "*/*", "user-agent": "WordPress/6.4.3; https://pages.promovaweb.com", "content-type": "application/x-www-form-urlencoded", "content-length": "106", "accept-encoding": "deflate, gzip, br", "x-forwarded-for": "*************", "x-forwarded-host": "data.promovaweb.com", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "004c98fc4927"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "28d5987d-4623-4275-bb41-1c015ee32b61", "connections": {"LeadData": {"main": [[{"node": "CreateContactMautic", "type": "main", "index": 0}]]}, "LeadMauticDNC": {"main": [[{"node": "End", "type": "main", "index": 0}]]}, "WordpressForm": {"main": [[{"node": "LeadData", "type": "main", "index": 0}]]}, "CheckEmailValid": {"main": [[], [{"node": "LeadMauticDNC", "type": "main", "index": 0}]]}, "CreateContactMautic": {"main": [[{"node": "CheckEmail<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}