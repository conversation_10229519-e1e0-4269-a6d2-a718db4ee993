{"id": "ikxQzs58WxtUjbuE", "meta": {"instanceId": "494d0146a0f47676ad70a44a32086b466621f62da855e3eaf0ee51dee1f76753", "templateCredsSetupCompleted": true}, "name": "Entra Contacts to <PERSON><PERSON><PERSON> User Sync", "tags": [], "nodes": [{"id": "5cf615cd-d555-4efa-bde4-5e1e2bda0c71", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-2120, 160], "parameters": {}, "typeVersion": 1}, {"id": "334e380c-ce6b-48c6-be2a-e688cd52ef14", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1700, 380], "parameters": {"width": 1635.910561370123, "height": 329.7269624573379, "content": "## Select Entra Contacts that should be synced to <PERSON><PERSON><PERSON>\n\n\n\n"}, "typeVersion": 1}, {"id": "39b9ace7-97ba-4202-9c7a-752f67c6a60b", "name": "Zammad Univeral User Object", "type": "n8n-nodes-base.set", "position": [-500, 500], "parameters": {"values": {"number": [{"name": "entra_key", "value": "={{ $json.id }}"}], "string": [{"name": "email", "value": "={{ $json.mail }}"}, {"name": "lastname", "value": "={{ $json.surname }}"}, {"name": "firstname", "value": "={{ $json.givenName }}"}, {"name": "mobile", "value": "={{ $json.phones[1].number }}"}, {"name": "phone", "value": "={{ $json.phones[2].number }}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "df5b4173-e1bd-49ce-9827-1b51a5e98da0", "name": "Get Zammad Users", "type": "n8n-nodes-base.zammad", "position": [-1000, 160], "parameters": {"filters": {}, "operation": "getAll", "returnAll": true}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "7f1d3b74-48dd-4245-9f17-286bcc552047", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [200, 180], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "email"}, "typeVersion": 3}, {"id": "c98c849e-8b0c-45b0-9b05-6b648bf9329c", "name": "Find new Zammad Users", "type": "n8n-nodes-base.compareDatasets", "position": [200, 460], "parameters": {"options": {}, "mergeByFields": {"values": [{"field1": "email", "field2": "email"}]}}, "typeVersion": 2.3}, {"id": "079437ca-a379-48ba-a4e5-52b87454adba", "name": "Update <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.zammad", "position": [560, 180], "parameters": {"id": "={{ $json.id }}", "operation": "update", "updateFields": {"phone": "={{ $json.phone }}", "mobile": "={{ $json.mobile }}", "lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}, {"name": "entra_object_type", "value": "contact"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "********-c9c2-4899-a6a8-dc6e89a87c5c", "name": "Create <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.zammad", "position": [580, 480], "parameters": {"lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "additionalFields": {"email": "={{ $json.email }}", "phone": "={{ $json.phone }}", "mobile": "={{ $json.mobile }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}, {"name": "entra_object_type", "value": "contact"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "6c8ef4d9-7bb2-4090-a0cf-411269f766aa", "name": "Deactivate Zammad User", "type": "n8n-nodes-base.zammad", "position": [600, 860], "parameters": {"id": "={{ $json.id }}", "operation": "update", "updateFields": {"phone": "={{ $json.phone }}", "active": false, "mobile": "={{ $json.mobile }}", "lastname": "={{ $json.lastname }}", "firstname": "={{ $json.firstname }}", "customFieldsUi": {"customFieldPairs": [{"name": "entra_key", "value": "={{ $json.entra_key }}"}, {"name": "entra_object_type", "value": "contact"}]}}}, "credentials": {"zammadTokenAuthApi": {"id": "fj5GuzcJuNLQeMxz", "name": "<PERSON><PERSON><PERSON> account"}}, "typeVersion": 1}, {"id": "e3db9840-666a-42d3-9466-70e970a57f29", "name": "Find removed Users", "type": "n8n-nodes-base.compareDatasets", "position": [200, 880], "parameters": {"options": {}, "resolve": "preferInput1", "mergeByFields": {"values": [{"field1": "entra_key", "field2": "entra_key"}]}}, "typeVersion": 2.3}, {"id": "6100fd80-8d9c-44ed-bfc1-6d39794c5405", "name": "Get Contacts from Entra", "type": "n8n-nodes-base.httpRequest", "position": [-1600, 500], "parameters": {"url": "https://graph.microsoft.com/v1.0/contacts", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "microsoftOAuth2Api"}, "credentials": {"microsoftOAuth2Api": {"id": "U2E5p3lreqSi8v1N", "name": "ms365test.zammad.org"}, "microsoftGraphSecurityOAuth2Api": {"id": "b09tqOxzkl0P8UQD", "name": "ms365test.zammad.org"}}, "typeVersion": 4.2}, {"id": "27e598a5-39a5-408b-a8ab-bf0ada9a6870", "name": "Entra Contacts", "type": "n8n-nodes-base.splitOut", "position": [-1220, 500], "parameters": {"options": {}, "fieldToSplitOut": "value"}, "typeVersion": 1}, {"id": "ac38d73e-7a71-4b7d-913e-abc96236b124", "name": "Filter contacts if needed", "type": "n8n-nodes-base.if", "position": [-840, 500], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "15da9b4f-46fa-4e9b-bd33-40ae79b88cd5", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "28d3faf9-7cf4-4470-941b-abada3de3b9c", "name": "Filter if needed", "type": "n8n-nodes-base.if", "position": [-400, 160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5b258616-66b2-4378-8215-5dce8edd19b3", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.entra_object_type }}", "rightValue": "contact"}, {"id": "0d569bde-d384-48d0-a208-aa707752d6e5", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.active }}", "rightValue": ""}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "55f3ac0f-d15e-4643-a1ca-26f4ade7bb14", "connections": {"Merge": {"main": [[{"node": "Update <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Entra Contacts": {"main": [[{"node": "Filter contacts if needed", "type": "main", "index": 0}]]}, "Filter if needed": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Find new Zammad Users", "type": "main", "index": 0}, {"node": "Find removed Users", "type": "main", "index": 0}]]}, "Get Zammad Users": {"main": [[{"node": "Filter if needed", "type": "main", "index": 0}]]}, "Find removed Users": {"main": [[{"node": "Deactivate Zammad User", "type": "main", "index": 0}]]}, "Find new Zammad Users": {"main": [[], [], [], [{"node": "Create <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Contacts from Entra": {"main": [[{"node": "Entra Contacts", "type": "main", "index": 0}]]}, "Filter contacts if needed": {"main": [[{"node": "Zammad Univeral User Object", "type": "main", "index": 0}]]}, "Zammad Univeral User Object": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "Find new Zammad Users", "type": "main", "index": 1}, {"node": "Find removed Users", "type": "main", "index": 1}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Zammad Users", "type": "main", "index": 0}, {"node": "Get Contacts from Entra", "type": "main", "index": 0}]]}}}