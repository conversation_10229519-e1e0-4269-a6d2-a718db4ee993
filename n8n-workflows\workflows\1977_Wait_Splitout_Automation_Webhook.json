{"id": "sB6dC0GZ7zZHuMGF", "meta": {"instanceId": "a9f3b18652ddc96459b459de4fa8fa33252fb820a9e5a1593074f3580352864a", "templateCredsSetupCompleted": true}, "name": "Test Webhooks in n8n Without Changing WEBHOOK_URL (PostBin & BambooHR Example)", "tags": [{"id": "qtD3SYKEoYtiqguT", "name": "building_blocks", "createdAt": "2025-02-08T21:20:40.051Z", "updatedAt": "2025-02-08T21:20:40.051Z"}, {"id": "mCgqKYNfNWwqIQG3", "name": "ai", "createdAt": "2025-02-08T21:20:49.438Z", "updatedAt": "2025-02-08T21:20:49.438Z"}, {"id": "EjQkfx3v7nH79HWo", "name": "hr", "createdAt": "2025-02-08T21:20:57.598Z", "updatedAt": "2025-02-08T21:20:57.598Z"}, {"id": "suSDrJxibUi10zsu", "name": "engineering", "createdAt": "2025-02-08T21:21:43.564Z", "updatedAt": "2025-02-08T21:21:43.564Z"}], "nodes": [{"id": "2529ea94-8427-4fbb-bac0-79fec29fe943", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [440, 1220], "parameters": {}, "typeVersion": 1}, {"id": "067ce1b6-a511-448b-a268-7d0869ed2b36", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [600, 980], "parameters": {"color": 6, "width": 550.7128407259806, "height": 151.03568930452542, "content": "### Requirements:\n1. **BambooHR instance** ([free trial link](https://www.bamboohr.com/signup/))\n2. **BambooHR API key*** ([documentation](https://documentation.bamboohr.com/docs/getting-started#authentication))\n3. **Slack connection** ([n8n documentation](https://docs.n8n.io/integrations/builtin/credentials/slack/))\n* **Note about API key**: Set up in n8n as Generic Credential (Basic Auth) with the API key as the username and any string for the password.\n\n"}, "typeVersion": 1}, {"id": "62a65021-8bc5-4bd3-95e4-b0616c0cbbe6", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1620, 1620], "parameters": {"color": 7, "width": 804.2810233962304, "height": 154.2786603126325, "content": "## Other use cases for BambooHR webhook\n1. Fraud & Compliance Monitoring (Triggered by Pay Rate, Pay Type, Compensation Change Reason, Bonus Amount, Commission Amount)\n2. Offboarding & Security Access Revocation (Triggered by Employment Status, Job Title, Department, Location)\n3. Manager Change Alert for Team & Workflow Updates (Triggered by Reporting To, Job Title, Department)"}, "typeVersion": 1}, {"id": "63e5f28a-83ea-44be-ad91-ab2b635551a1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [800, 1140], "parameters": {"color": 7, "width": 600.2141303561856, "height": 246.1007234368067, "content": "## Create a new Bin in PostBin (STEP #1 above)"}, "typeVersion": 1}, {"id": "1afbac45-116e-4c8b-886c-24a96ba286ab", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1680, 1240], "parameters": {"mode": "combine", "options": {}, "combineBy": "combineByPosition"}, "typeVersion": 3}, {"id": "367315b0-eba5-4768-bdb0-8be23d965f6c", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1840, 1180], "parameters": {"color": 7, "width": 424.9937286279833, "height": 248.92215299422725, "content": "## Register webhook (STEP #2 above)"}, "typeVersion": 1}, {"id": "5b860a4e-66c9-4996-bd8f-ac642eca9021", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1400, 300], "parameters": {"color": 4, "width": 291.16380512688715, "height": 397.605174332017, "content": "## STEP #3: Confirm webhook functionality"}, "typeVersion": 1}, {"id": "c6d78f60-0e05-452c-b50d-4bee9b4e1220", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [600, 300], "parameters": {"color": 4, "width": 611.7032537942721, "height": 397.94343220191183, "content": "## STEP #1: Create a new Bin in PostBin\nNo authentication needed. Use API to create Bin and retrieve BinId to craft URL for subsequent usage."}, "typeVersion": 1}, {"id": "6bdd564e-daf7-4259-a283-547f8257dcce", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [580, 140], "parameters": {"color": 7, "width": 1140.1894415469083, "height": 593.490746966612, "content": "## How to Test a Short-Lived Webhook in n8n **WITHOUT** Changing WEBHOOK_URL\nTypically in n8n, in order to test a webhook, you first need to go through the process of changing the [**WEBHOOK_URL**](https://docs.n8n.io/hosting/configuration/configuration-examples/webhook-url/) environment variable to an address that is accessible to the service you want to test. Time permitting, that can be done with [ngrok](https://ngrok.com/docs/getting-started/) ([example](https://docs.n8n.io/hosting/installation/server-setups/)) or by self-hosting with one of [n8n's recommended deployment options](https://docs.n8n.io/hosting/installation/server-setups/).\n\nBut if you're new to n8n and in a rush to test a webhook's functionality, you can use [PostBin](https://www.postb.in/) as demonstrated in this workflow to test a proof of concept fast and avoid any unnecessary time on n8n setup and configuration."}, "typeVersion": 1}, {"id": "06b12932-bc46-46ff-a316-518cd1e24546", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1420, 600], "parameters": {"color": 2, "width": 255.54164387152053, "height": 80, "content": "**This may respond with a 404**\nIf no requests have been sent to the Bin, an error is raised."}, "typeVersion": 1}, {"id": "17eabcf5-9ae7-4e79-bdb5-3664fa286aeb", "name": "Create Bin", "type": "n8n-nodes-base.httpRequest", "position": [660, 420], "parameters": {"url": "https://www.postb.in/api/bin", "method": "POST", "options": {}}, "typeVersion": 4.2}, {"id": "5b233ff1-475a-48a7-a5d2-4ce82adb2213", "name": "GET Bin", "type": "n8n-nodes-base.postBin", "position": [860, 420], "parameters": {"binId": "={{ $json.binId }}", "operation": "get", "requestOptions": {}}, "typeVersion": 1}, {"id": "14e0b2fc-f1bb-4eae-be81-069641f27b53", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [620, 600], "parameters": {"color": 7, "width": 182.23771342026427, "height": 80, "content": "Uses API call to bypass broken PostBin create bin endpoint in n8n."}, "typeVersion": 1}, {"id": "2eb51697-744e-4bfc-ae3e-ad28bcdc21b1", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [840, 600], "parameters": {"color": 7, "width": 351.0986223154297, "height": 80, "content": "Retrieve the binId (can also be found in response of Create Bin node). Craft a url that uses `https://www.postb.in/:binId` structure"}, "typeVersion": 1}, {"id": "ae7367be-ca86-4cac-a763-3627a176d988", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [580, 740], "parameters": {"width": 631.0482952232512, "height": 113.5322633928848, "content": "**Per PostBin API Documentation:**\nYou can hit the https://www.postb.in/:binId endpoint to collect any kind of request data whether it is a GET, POST, PUT, PATCH, DELETE or whatever. This particular endpoint is not RESTful and is not part of this API. It isn't RESTful by definition. ie. it is meant to collect whatever you send to it."}, "typeVersion": 1}, {"id": "be327737-1e33-4107-9f98-66a6d66d2886", "name": "Format url for webhook", "type": "n8n-nodes-base.set", "position": [1060, 420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5235b8f1-f284-472f-b6a5-25c16bc4a66e", "name": "webhook_url", "type": "string", "value": "=https://www.postb.in/{{ $json.binId }}"}, {"id": "35d56f07-4f6b-422a-8a03-0c3e49f4d734", "name": "binId", "type": "string", "value": "={{ $json.binId }}"}]}}, "typeVersion": 3.4}, {"id": "463d247c-ac97-4d79-a0c9-8c0785240a73", "name": "GET most recent request", "type": "n8n-nodes-base.postBin", "position": [1500, 420], "parameters": {"binId": "={{ $('Format url for webhook').item.json.binId }}", "resource": "request", "operation": "remove<PERSON><PERSON><PERSON>", "requestOptions": {}}, "typeVersion": 1}, {"id": "ef07fa4e-1411-474e-ba98-171abae9542d", "name": "MOCK request", "type": "n8n-nodes-base.postBin", "position": [1260, 580], "parameters": {"binId": "={{ $('Format url for webhook').item.json.binId }}", "resource": "request", "operation": "send", "binContent": "=", "requestOptions": {}}, "typeVersion": 1}, {"id": "6769b161-6dff-4732-b1cd-900b2e64ffc9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [580, 900], "parameters": {"color": 4, "width": 4124.530158203355, "height": 962.561104644939, "content": "## Example: Register and test a webhook in BambooHR\n### Sc<PERSON>rio: Send a notification to S<PERSON>ck when new employees join the company"}, "typeVersion": 1}, {"id": "a7f57c0a-3918-450b-b1a7-edd80e6edcf6", "name": "Create Bin1", "type": "n8n-nodes-base.httpRequest", "position": [860, 1220], "parameters": {"url": "https://www.postb.in/api/bin", "method": "POST", "options": {}}, "typeVersion": 4.2}, {"id": "8a9ef96b-eb99-4fe5-aa82-0b4453d90dff", "name": "GET Bin1", "type": "n8n-nodes-base.postBin", "position": [1060, 1220], "parameters": {"binId": "={{ $json.binId }}", "operation": "get", "requestOptions": {}}, "typeVersion": 1}, {"id": "c70ff70f-80c6-4516-b278-bad82655d78c", "name": "Format url for webhook1", "type": "n8n-nodes-base.set", "position": [1260, 1220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "5235b8f1-f284-472f-b6a5-25c16bc4a66e", "name": "url", "type": "string", "value": "=https://www.postb.in/{{ $json.binId }}"}, {"id": "35d56f07-4f6b-422a-8a03-0c3e49f4d734", "name": "binId", "type": "string", "value": "={{ $json.binId }}"}]}}, "typeVersion": 3.4}, {"id": "793cd3ab-1459-4382-b9f7-5630869a871e", "name": "SET BambooHR subdomain", "type": "n8n-nodes-base.set", "position": [660, 1480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "89c9eb04-196b-4cb0-afec-dab071dcc471", "name": "subdomain", "type": "string", "value": "example"}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "06703339-8e5b-4267-ae23-15540ea00692", "name": "Split out fields", "type": "n8n-nodes-base.splitOut", "position": [1060, 1480], "parameters": {"options": {}, "fieldToSplitOut": "fields"}, "typeVersion": 1}, {"id": "b8086b64-0e27-4294-a230-3d6f428a2ddb", "name": "Combine fields to monitor", "type": "n8n-nodes-base.aggregate", "position": [1460, 1480], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "monitorFields", "fieldToAggregate": "alias"}]}}, "typeVersion": 1}, {"id": "6c75204f-8527-467f-b982-bed268843fde", "name": "Format payload for BambooHR webhook", "type": "n8n-nodes-base.set", "position": [1900, 1240], "parameters": {"include": "except", "options": {}, "assignments": {"assignments": [{"id": "188d1a10-d32c-4e48-8bad-f8a5002c34a9", "name": "name", "type": "string", "value": "Webhook Test"}, {"id": "cfcd6de9-c20f-4935-8b5f-548bd6c381bf", "name": "format", "type": "string", "value": "json"}, {"id": "c0b22bc7-d873-4973-9e27-6931dde4b8b1", "name": "limit.times", "type": "number", "value": 1}, {"id": "5e912e0a-d3fe-46e5-b85a-b22be0ae3eb1", "name": "limit.seconds", "type": "number", "value": 60}, {"id": "0a197fcf-4d30-4112-a441-5ee4dbfaa350", "name": "postFields", "type": "object", "value": "={{ {\"employeeNumber\": \"Employee #\",\n        \"firstName\": \"First name\",\n        \"lastName\": \"Last name\",\n        \"jobTitle\": \"Job title\"} }}"}, {"id": "aa292476-0ee2-49fc-afce-4788ff37475a", "name": "frequency", "type": "object", "value": "={\n  \"hour\": null,\n  \"minute\": null,\n  \"day\": null,\n  \"month\": null\n}"}, {"id": "0e6c44e5-c918-4897-b865-5e1848ff8444", "name": "subdomain", "type": "string", "value": "={{ $('SET BambooHR subdomain').first().json.subdomain }}"}]}, "excludeFields": "binId", "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "b0191582-e8d3-4432-b8e8-38ff0fc782fb", "name": "Create webhook in BambooHR", "type": "n8n-nodes-base.httpRequest", "position": [2100, 1240], "parameters": {"url": "=https://api.bamboohr.com/api/gateway.php/{{ $json.subdomain }}/v1/webhooks/", "method": "POST", "options": {}, "jsonBody": "={{ $json.removeField(\"subdomain\").toJsonString() }}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "XXXXXX", "name": "BambooHR Basic Auth"}}, "typeVersion": 4.2}, {"id": "6f8d47f3-1a80-4317-a9eb-89188c70618c", "name": "Create dummy data for employees", "type": "n8n-nodes-base.debugHelper", "position": [2380, 1240], "parameters": {"category": "randomData", "randomDataCount": 3}, "typeVersion": 1}, {"id": "b3ba2315-f7d7-474b-9f06-3dbad510fb93", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [800, 1407.2486467347771], "parameters": {"color": 7, "width": 794.510445997778, "height": 368.01097806266364, "content": "## GET fields from BambooHR to monitor for changes [[src]](https://documentation.bamboohr.com/reference/get-monitor-fields)"}, "typeVersion": 1}, {"id": "34956bf7-ef81-425b-a348-bffa99f278bd", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2320, 1180], "parameters": {"color": 7, "width": 416.47592441009544, "height": 250.72353860519, "content": "## Test webhook"}, "typeVersion": 1}, {"id": "077934b0-21c5-49ef-9482-fa52ecbe917f", "name": "Keep only new employee fields", "type": "n8n-nodes-base.filter", "position": [1260, 1480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e1daab1a-bee5-4308-82f9-6660e957722d", "operator": {"type": "array", "operation": "contains", "rightType": "any"}, "leftValue": "={{ [\"employmentHistoryStatus\",\"employeeStatusDate\",\"hireDate\",\"originalHireDate\"] }}", "rightValue": "={{ $json.alias }}"}]}}, "typeVersion": 2.2}, {"id": "ecd27c9d-fe7a-45fa-b085-e68535c334af", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [820, 1653.074026323302], "parameters": {"width": 568.1578343498747, "height": 101.29440680672363, "content": "### Note about this section\nDepending on your familiarity with BambooHR and your intention with the webhook, you could hard code the fields to monitor with your webhook or use AI to filter based on topic. I chose a middle ground for this example.\n"}, "typeVersion": 1}, {"id": "2b0ee3a5-1b9f-4f8f-b024-2c576573d2d6", "name": "GET all possible fields to monitor in BambooHR", "type": "n8n-nodes-base.httpRequest", "position": [860, 1480], "parameters": {"url": "=https://api.bamboohr.com/api/gateway.php/{{ $json.subdomain }}/v1/webhooks/monitor_fields", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "XXXXXX", "name": "BambooHR Basic Auth"}}, "typeVersion": 4.2}, {"id": "97923d51-c895-4215-b808-4ade22ea6011", "name": "Register and test webhook", "type": "n8n-nodes-base.noOp", "position": [1260, 420], "parameters": {}, "typeVersion": 1}, {"id": "4e48efac-eec6-48cc-b940-b04bda667953", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1228.9696450873366, 300], "parameters": {"color": 4, "width": 157.46160832218783, "height": 397.57230173351894, "content": "## STEP #2\nUse the PostBin URL in place of your normal webhook"}, "typeVersion": 1}, {"id": "f7147f00-19d1-4c0f-a75b-a7fd18f16c31", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [2940, 1040], "parameters": {"color": 7, "width": 296.68826711085643, "height": 497.77627578351644, "content": "## STEP #3: Confirm webhook functionality"}, "typeVersion": 1}, {"id": "ea428b8f-fb4c-44bd-bcf0-bb7f40f3ed98", "name": "Check BambooHR for calls to webhook", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [3040, 1140], "parameters": {"url": "=https://api.bamboohr.com/api/gateway.php/{{ $('Format payload for BambooHR webhook').item.json.subdomain }}/v1/webhooks/{{ $('Create webhook in BambooHR').item.json.id }}/log", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "XXXXXX", "name": "BambooHR Basic Auth"}}, "typeVersion": 4.2}, {"id": "2aa4610d-48a8-4c15-be21-1adbb3bb8b1a", "name": "Create employee records with dummy data", "type": "n8n-nodes-base.bambooHr", "position": [2580, 1240], "parameters": {"lastName": "={{ $json.lastname }}", "firstName": "={{ $json.firstname }}", "additionalFields": {"hireDate": "={{ $now }}", "department": 18264}}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "f912f38c-fb3b-4357-87fe-cca9aea7ebf4", "name": "Split out employees", "type": "n8n-nodes-base.splitOut", "position": [3300, 1340], "parameters": {"options": {}, "fieldToSplitOut": "body.employees"}, "typeVersion": 1}, {"id": "200f8afe-f872-4598-b376-6e5cd053aa7d", "name": "Format displayName", "type": "n8n-nodes-base.set", "position": [3500, 1340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "41e8a654-af0e-42db-a9f8-23bc951d34a9", "name": "displayName", "type": "string", "value": "={{ $json.fields[\"First name\"].value + \" \" +  $json.fields[\"Last name\"].value}}"}]}}, "typeVersion": 3.4}, {"id": "5fdf5f56-42ea-4891-9b66-5d3d290d0862", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4100, 1480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "c3b02b2a-2135-41da-a881-25cf2135ff71", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [4200, 1480], "parameters": {}, "typeVersion": 1}, {"id": "e32e3977-1a4c-4b74-839e-278621ac59ec", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4200, 1640], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "aaf59e1e-4db6-416b-8602-d5dab0959783", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [4420, 1640], "parameters": {"jsonSchemaExample": "{\n\t\"welcome_message\": \"We are excited to welcome employee_name to the company!\"\n}"}, "typeVersion": 1.2}, {"id": "cc6702aa-8e96-40ad-805e-306e94b0be13", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [4100, 1340], "parameters": {"text": "=Write a message to be shared with other employees welcoming our new {{ $json.keys().first() + \": \" + $json.values().first().join(', ').replace(/ ([^,]*)$/, ' and $1') }} to the company.", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "7f2d7505-5554-4ea4-8bf9-1e05c56c2bc6", "name": "Combine employees into list", "type": "n8n-nodes-base.aggregate", "position": [3700, 1340], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "=employee", "fieldToAggregate": "displayName"}]}}, "typeVersion": 1}, {"id": "b897a173-254f-445f-a3af-db9398d0c904", "name": "Pluralize key", "type": "n8n-nodes-base.rename<PERSON><PERSON>s", "position": [3900, 1340], "parameters": {"keys": {"key": [{"newKey": "=employee{{ $if($json.employee.length > 1,\"s\",\"\") }}", "currentKey": "employee"}]}, "additionalOptions": {}}, "typeVersion": 1}, {"id": "7e0474f1-3a9a-4b30-91eb-0b0d107d8bd1", "name": "Welcome employees on Slack", "type": "n8n-nodes-base.slack", "position": [4480, 1340], "webhookId": "700f2d63-f04a-4809-9602-75f3328b56f8", "parameters": {"text": "={{ $json.output.welcome_message }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "C08BWLDFS48", "cachedResultName": "social"}, "otherOptions": {}}, "credentials": {"slackApi": {"id": "XXXXXX", "name": "Slack account"}}, "typeVersion": 2.2}, {"id": "99cd4b68-a789-4a78-9636-c26554d703ed", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [3260, 1240], "parameters": {"color": 7, "width": 1380.************, "height": 545.************, "content": "## (For example purposes) Send message to Slack channel welcoming new employees"}, "typeVersion": 1}, {"id": "37839a6d-b616-4e24-b24f-************", "name": "Sticky Note18", "type": "n8n-nodes-base.stickyNote", "position": [4340, 920], "parameters": {"color": 3, "width": 342.**************, "height": 275.**************, "content": "## FOR TESTING: <PERSON>LE<PERSON> WEBHOOK"}, "typeVersion": 1}, {"id": "0e03ab51-dace-4aed-9f4e-16fbbb7f7173", "name": "DELETE BambooHR webhook", "type": "n8n-nodes-base.httpRequest", "position": [4460, 1020], "parameters": {"url": "=https://api.bamboohr.com/api/gateway.php/{subdomain}/v1/webhooks/{webhook_id}", "method": "DELETE", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth"}, "credentials": {"httpBasicAuth": {"id": "XXXXXX", "name": "BambooHR Basic Auth"}}, "typeVersion": 4.2}, {"id": "81dc0021-3a7d-41f9-aef9-126143b51e9a", "name": "Sticky Note20", "type": "n8n-nodes-base.stickyNote", "position": [1840, 1440], "parameters": {"width": 424.0067532215409, "height": 134.02025779064905, "content": "BambooHR's `/webhook` API endpoint expects arguments passed in the body of the request. You can see what arguments are required in their [documentation](https://documentation.bamboohr.com/reference/post-webhook) and [examples](https://documentation.bamboohr.com/docs/webhook-api-permissioned). In the arguments we pass through, we have set our webhook to fire at the same frequency as BambooHR's rate limit: 1 time every 60 seconds."}, "typeVersion": 1}, {"id": "b4a246ff-200a-40bc-a79c-4f51b24e0948", "name": "Sticky Note21", "type": "n8n-nodes-base.stickyNote", "position": [2940, 1557.4137607776859], "parameters": {"width": 295.8585062958632, "height": 227.09133367749476, "content": "### What is this?\nIn the above two nodes, we performing two actions:\n\n1. Checking BambooHR for a record of calls made by it to the webhook URL we registered (provided by PostBin).\n2. Retrieving the most recent call made by BambooHR to the webhook URL from PostBin."}, "typeVersion": 1}, {"id": "b09041d0-42b7-4084-b336-5d9af288acf9", "name": "GET most recent request1", "type": "n8n-nodes-base.postBin", "onError": "continueRegularOutput", "position": [3040, 1340], "parameters": {"binId": "={{ $('Merge').item.json.binId }}", "resource": "request", "operation": "remove<PERSON><PERSON><PERSON>", "requestOptions": {}}, "typeVersion": 1}, {"id": "ee2543e5-5fc6-48e1-a574-d351380df732", "name": "Wait 60 + 1 seconds for webhook to fire", "type": "n8n-nodes-base.wait", "position": [2780, 1240], "webhookId": "61bbec81-dcf5-441e-b6dd-ad96b429e80d", "parameters": {"amount": 61}, "executeOnce": true, "typeVersion": 1.1}, {"id": "6f6a95ee-ec01-429c-8710-edc52b6cc185", "name": "Sticky Note19", "type": "n8n-nodes-base.stickyNote", "position": [1740, 780], "parameters": {"color": 5, "width": 256.0973815349037, "height": 87.34661077350344, "content": "## About the maker\n**[<PERSON> on LinkedIn](https://www.linkedin.com/in/ludwiggerdes)**"}, "typeVersion": 1}, {"id": "fc8344ab-f643-4bc2-af97-a2022834b3c8", "name": "Sticky Note22", "type": "n8n-nodes-base.stickyNote", "position": [1740, 520], "parameters": {"color": 7, "width": 255.71137685448693, "height": 240.80136668021893, "content": "![image](https://media1.giphy.com/media/v1.Y2lkPTc5MGI3NjExdjM4dWFjeW1lNGVxc2Vsd2Z0aXB4cWNzZmN6aG41Y296cXVnem10eCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/5cL9WAVQREF3TkfhXj/giphy.gif#full-width)"}, "typeVersion": 1}, {"id": "58c5c5a6-2210-4506-9470-d6a55fae421a", "name": "Sticky Note23", "type": "n8n-nodes-base.stickyNote", "position": [3280, 1517.2043765224669], "parameters": {"width": 410.05041971203013, "height": 251.31245942384516, "content": "## What's happening here?\nIn this section, we do the following:\n1. Extract employee information from webhook call (from PostBin)\n2. Create a displayName from each employee's first and last name\n3. Combine the names into a list and format the key\n4. Ask OpenAI to compose a welcome message with the employee names\n5. Post that welcome message to Slack"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "c8562d68-8706-4fe0-9983-b9ae6de379a0", "connections": {"Merge": {"main": [[{"node": "Format payload for BambooHR webhook", "type": "main", "index": 0}]]}, "GET Bin": {"main": [[{"node": "Format url for webhook", "type": "main", "index": 0}]]}, "GET Bin1": {"main": [[{"node": "Format url for webhook1", "type": "main", "index": 0}]]}, "Create Bin": {"main": [[{"node": "GET Bin", "type": "main", "index": 0}]]}, "Create Bin1": {"main": [[{"node": "GET Bin1", "type": "main", "index": 0}]]}, "Pluralize key": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Welcome employees on Slack", "type": "main", "index": 0}]]}, "Split out fields": {"main": [[{"node": "Keep only new employee fields", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Format displayName": {"main": [[{"node": "Combine employees into list", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Split out employees": {"main": [[{"node": "Format displayName", "type": "main", "index": 0}]]}, "Format url for webhook": {"main": [[{"node": "Register and test webhook", "type": "main", "index": 0}]]}, "SET BambooHR subdomain": {"main": [[{"node": "GET all possible fields to monitor in BambooHR", "type": "main", "index": 0}]]}, "Format url for webhook1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "GET most recent request1": {"main": [[{"node": "Split out employees", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Combine fields to monitor": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Register and test webhook": {"main": [[{"node": "GET most recent request", "type": "main", "index": 0}]]}, "Create webhook in BambooHR": {"main": [[{"node": "Create dummy data for employees", "type": "main", "index": 0}]]}, "Combine employees into list": {"main": [[{"node": "Pluralize key", "type": "main", "index": 0}]]}, "Keep only new employee fields": {"main": [[{"node": "Combine fields to monitor", "type": "main", "index": 0}]]}, "Create dummy data for employees": {"main": [[{"node": "Create employee records with dummy data", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create Bin1", "type": "main", "index": 0}, {"node": "SET BambooHR subdomain", "type": "main", "index": 0}]]}, "Format payload for BambooHR webhook": {"main": [[{"node": "Create webhook in BambooHR", "type": "main", "index": 0}]]}, "Create employee records with dummy data": {"main": [[{"node": "Wait 60 + 1 seconds for webhook to fire", "type": "main", "index": 0}]]}, "Wait 60 + 1 seconds for webhook to fire": {"main": [[{"node": "Check BambooHR for calls to webhook", "type": "main", "index": 0}, {"node": "GET most recent request1", "type": "main", "index": 0}]]}, "GET all possible fields to monitor in BambooHR": {"main": [[{"node": "Split out fields", "type": "main", "index": 0}]]}}}