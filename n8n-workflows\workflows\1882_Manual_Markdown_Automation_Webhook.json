{"id": "i89dNLYeOVdTwtcL", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Extract & Summarize Indeed Company Info with Bright Data and Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}, {"id": "rKOa98eAi3IETrLu", "name": "HR", "createdAt": "2025-04-13T04:59:30.580Z", "updatedAt": "2025-04-13T04:59:30.580Z"}], "nodes": [{"id": "f5b44c95-12f2-44c1-a736-034127a713bb", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [200, -440], "parameters": {}, "typeVersion": 1}, {"id": "c199c5a7-d015-4f48-9fef-a5a1e5b5acd4", "name": "Google Gemini Chat Model For Summarization", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1320, -260], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "f6c1d4a7-ed4c-412f-af26-8714171ecc62", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, -860], "parameters": {"width": 400, "height": 300, "content": "## Note\n\nDeals with the Indeed Company web scraping by utilizing Bright Data Web Unlocker Product.\n\nThe Basic LLM Chain, Summarization and AI Agent are being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to set the Indeed search query and update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "f9625614-1051-48ec-b406-8df920bb9b92", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, -860], "parameters": {"width": 480, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain Data Extractor.\n\nSummarization Chain is being used for the summarization of search results.\n\nThe AI Agent formats the search result and pushes it to the Webhook via HTTP Request"}, "typeVersion": 1}, {"id": "9697517c-6587-4279-a123-28ad8cd8a085", "name": "Set Indeed Search Query", "type": "n8n-nodes-base.set", "position": [440, -440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3aedba66-f447-4d7a-93c0-8158c5e795f9", "name": "search_query", "type": "string", "value": "Starbucks"}, {"id": "4e7ee31d-da89-422f-8079-2ff2d357a0ba", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "typeVersion": 3.4}, {"id": "23122a41-d127-4e19-951c-4e143db2c5e6", "name": "Perform Indeed Web Request", "type": "n8n-nodes-base.httpRequest", "position": [720, -440], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "=https://www.indeed.com/cmp/{{ encodeURI($json.search_query) }}?product=unlocker&method=api"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "markdown"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "38a9c763-666e-4e0c-9b16-9205a7fa2d23", "name": "Indeed Expert AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1680, -440], "parameters": {"text": "=You are an Indeed Expert. You need to format the search result  and push it to the Webhook via HTTP Request. Here is the search result - {{ $('Markdown to Textual Data Extractor').item.json.text }}", "options": {}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "0715b1ee-c377-43f4-8353-11188cb9dbf7", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1040, -220], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "8fab1a0e-c550-4167-be2f-3a9eeaf49111", "name": "Markdown to Textual Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [940, -440], "parameters": {"text": "=You need to analyze the below markdown and convert to textual data.\n\n{{ $json.data }}", "messages": {"messageValues": [{"message": "You are a markdown expert"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "e49296ca-b88b-4db7-864d-9431312d74f3", "name": "Indeed Summarization", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1320, -440], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "53233fe9-5f70-4df8-82c3-7ef84d136e04", "name": "Convert Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [1180, -820], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.data }}"}, "typeVersion": 1}, {"id": "6e681d88-dc8c-4087-ae03-45e91dd925cd", "name": "Initiate a Webhook Notification for Markdown to HTML Response", "type": "n8n-nodes-base.httpRequest", "position": [1440, -820], "parameters": {"url": "https://webhook.site/daf9d591-a130-4010-b1d3-0c66f8fcf467", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "html_response", "value": "={{ $json.data }}"}]}}, "typeVersion": 4.2}, {"id": "ac059d7a-f4e0-43d6-a056-933a4696553b", "name": "Google Gemini Chat Model for AI Agent", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1620, -200], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "d77cad4d-8899-4345-bf29-ba21cef946cd", "name": "Initiate a Webhook Request", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1920, -200], "parameters": {"url": "https://webhook.site/daf9d591-a130-4010-b1d3-0c66f8fcf467", "method": "POST", "sendBody": true, "parametersBody": {"values": [{"name": "search_summary", "value": "={{ $json.response.text }}", "valueProvider": "fieldValue"}, {"name": "search_result"}]}, "toolDescription": "Extract the response and format a structured JSON response"}, "typeVersion": 1.1}, {"id": "b94deec3-3394-4fb3-b700-9ed3ced877ca", "name": "Initiate a Webhook Notification for Summarization", "type": "n8n-nodes-base.httpRequest", "position": [1780, -700], "parameters": {"url": "https://webhook.site/daf9d591-a130-4010-b1d3-0c66f8fcf467", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "dd804e78-abaa-48f4-82ab-6dbfdec43ef3", "connections": {"Indeed Summarization": {"main": [[{"node": "Indeed Expert AI Agent", "type": "main", "index": 0}, {"node": "Initiate a Webhook Notification for Summarization", "type": "main", "index": 0}]]}, "Indeed Expert AI Agent": {"main": [[]]}, "Set Indeed Search Query": {"main": [[{"node": "Perform Indeed Web Request", "type": "main", "index": 0}]]}, "Convert Markdown to HTML": {"main": [[{"node": "Initiate a Webhook Notification for Markdown to HTML Response", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Markdown to Textual Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Initiate a Webhook Request": {"ai_tool": [[{"node": "Indeed Expert AI Agent", "type": "ai_tool", "index": 0}]]}, "Perform Indeed Web Request": {"main": [[{"node": "Markdown to Textual Data Extractor", "type": "main", "index": 0}, {"node": "Convert Markdown to HTML", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Indeed Search Query", "type": "main", "index": 0}]]}, "Markdown to Textual Data Extractor": {"main": [[{"node": "Indeed Summarization", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for AI Agent": {"ai_languageModel": [[{"node": "Indeed Expert AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model For Summarization": {"ai_languageModel": [[{"node": "Indeed Summarization", "type": "ai_languageModel", "index": 0}]]}}}