{"id": "pcLi17oUJK9pSaee", "meta": {"instanceId": "10ac0d272b984a3e01d44645b4be41105d42352c9db9f4c0c7f5be7946b87d41", "templateCredsSetupCompleted": true}, "name": "Web Server Monitor.", "tags": [], "nodes": [{"id": "014e1202-3822-4d3f-817e-31f64c8bd5f5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-680, -440], "parameters": {"width": 560, "height": 540, "content": "📘 Node Descriptions for Your Web Server Monitor Workflow\n\n⏰ 1. Schedule Trigger  \nTriggers the workflow every minute to initiate regular checks on server availability.\n\n📄 2. Web Servers List (Google Sheets)  \nFetches a list of server hostnames or IP addresses from a Google Sheet.  \nEach row is treated as one server. This makes server management easy — no need to edit the workflow to add/remove servers.\n\n🌐 3. Server Alive Check (HTTP) \nSends an HTTP GET request to each server (e.g., http://your-server.com).  \nIf the request fails, the error path is triggered.  \n\n📝 4. Web Server Alive Log (Google Sheets)  \nLogs successful server checks into a separate Sheet with a timestamp.\nThis log helps track uptime history, verify server health, and generate availability reports.\n\n🚨📧 5. Server Down Notification (Gmail)  \nSends an alert email if a server does not respond or returns an error.  \nIncludes the server address and the timestamp of failure.\n\n📝 6. Web Server Down Log (Google Sheets)\nLogs the failed server checks into another Sheet with a timestamp.  \nUseful for uptime reporting, debugging, and audit tracking.\n"}, "typeVersion": 1}, {"id": "94a3454c-69bd-4a5d-b169-8f3772a41321", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 0], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "typeVersion": 1.2}, {"id": "f92fcadf-0b13-42ac-abed-aaf169d0ed76", "name": "Server-Monitor", "type": "n8n-nodes-base.googleSheets", "position": [220, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit#gid=*********", "cachedResultName": "Server_List"}, "documentId": {"__rl": true, "mode": "list", "value": "1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit?usp=drivesdk", "cachedResultName": "Server-Monitor"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "8cXGgTelVK5DewVr", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "c168a1f9-1f3f-40b8-95d0-51f6259d8096", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [440, 0], "parameters": {"url": "=http://{{ $json.Server }}", "options": {}}, "typeVersion": 4.2}, {"id": "0ac82373-6958-4de9-8cf7-94b0005197ff", "name": "Server_Status_Alive", "type": "n8n-nodes-base.googleSheets", "position": [660, -180], "parameters": {"columns": {"value": {"Status": "Alive", "TimeStamp": "={{ $now.format('yyyy-MM-dd') }}", "Server IP Address": "={{ $('Server-Monitor').item.json.Server }}"}, "schema": [{"id": "TimeStamp", "type": "string", "display": true, "required": false, "displayName": "TimeStamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Server IP Address", "type": "string", "display": true, "required": false, "displayName": "Server IP Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit#gid=*********", "cachedResultName": "Server_Status_Alive"}, "documentId": {"__rl": true, "mode": "list", "value": "1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit?usp=drivesdk", "cachedResultName": "Server-Monitor"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "8cXGgTelVK5DewVr", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "6dc31115-4ab6-44cf-ac4f-e2af82a5355e", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [660, 100], "webhookId": "dec1def3-c858-4a43-b96e-2655d3fa3b77", "parameters": {"message": "=Hi Team,\n\nAt {{$now.format('yyyy-MM-dd HH:mm:ss')}}, the following server failed to respond to ping:\n\n🔻 Server Down: {{ $json[\"Server\"] }}  \n\nPlease investigate immediately to prevent service disruption. \n\nAutomated Monitoring System\n", "options": {}, "subject": "=🔻 Server Down: {{ $json[\"Server\"] }}: {{ $today.format('yyyy-MM-dd') }}"}, "credentials": {"gmailOAuth2": {"id": "C1RVeb9JgdvkMkP4", "name": "Gmail account 2"}}, "typeVersion": 2.1}, {"id": "********-57a2-4c4d-9a10-89f4f6ee4ed7", "name": "Server_Status_Down", "type": "n8n-nodes-base.googleSheets", "position": [880, 100], "parameters": {"columns": {"value": {"Status": "Down", "TimeStamp": "={{$now.format('yyyy-MM-dd HH:mm:ss')}}", "Server IP Address": "={{ $('Server-Monitor').item.json.Server }}"}, "schema": [{"id": "TimeStamp", "type": "string", "display": true, "required": false, "displayName": "TimeStamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Server IP Address", "type": "string", "display": true, "required": false, "displayName": "Server IP Address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit#gid=0", "cachedResultName": "Server_Status_Down"}, "documentId": {"__rl": true, "mode": "list", "value": "1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1OiwBkf3bs3tcfi5cAIrOl_GrXw2EfQLdcPbh6SaBFKQ/edit?usp=drivesdk", "cachedResultName": "Server-Monitor"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "8cXGgTelVK5DewVr", "name": "Google Sheets account"}}, "typeVersion": 4.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "********-4434-4a0c-a3c4-9068baccc3cc", "connections": {"Gmail": {"main": [[{"node": "Server_Status_Down", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Server_Status_Alive", "type": "main", "index": 0}], [{"node": "Gmail", "type": "main", "index": 0}]]}, "Server-Monitor": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Server-Monitor", "type": "main", "index": 0}]]}}}