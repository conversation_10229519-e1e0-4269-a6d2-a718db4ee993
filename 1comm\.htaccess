# Allow larger uploads and secure the uploads folder
# --------------------------------------------------

# --- PHP configuration (works only if <PERSON><PERSON> is run as Apache module) ---
# Raise upload size limits for this directory only
php_value upload_max_filesize 50M
php_value post_max_size       52M

# --- Security hardening ---
# 1. Disable script execution inside the uploads folder
RemoveHandler .php .phtml .php3 .pl .py .cgi .asp .aspx .jsp .sh
SetHandler default-handler
AddType text/plain .php .phtml .php3 .pl .py .cgi .asp .aspx .jsp .sh

# 2. Prevent directory listing
Options -Indexes

# 3. Deny access to .htaccess itself
<Files ".htaccess">
  Require all denied
</Files>
