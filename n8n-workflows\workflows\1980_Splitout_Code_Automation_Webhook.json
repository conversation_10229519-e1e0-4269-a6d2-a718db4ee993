{"id": "siXUnQhJpCJ9rHzu", "meta": {"instanceId": "a9f3b18652ddc96459b459de4fa8fa33252fb820a9e5a1593074f3580352864a", "templateCredsSetupCompleted": true}, "name": "Auto-Tag Blog Posts in WordPress with AI", "tags": [{"id": "ijuVOmJpw5mCrzQX", "name": "marketing", "createdAt": "2025-01-28T16:42:03.029Z", "updatedAt": "2025-01-28T16:42:03.029Z"}], "nodes": [{"id": "0561d80b-f360-4a8e-930d-49b778833991", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3260, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "yWpagxp5s8o3dlBp", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "d71aec64-299c-4258-8eb4-95821d15b758", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [3460, 540], "parameters": {}, "typeVersion": 1}, {"id": "1468a001-ca7b-4726-ae31-02b28d78b07e", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3360, 680], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "yWpagxp5s8o3dlBp", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "bb4221ad-94d7-4543-850f-87b83735d2a6", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [3560, 760], "parameters": {"jsonSchemaExample": "{\n\t\"tags\": [\"Germany\", \"Technology\", \"Workflow Automation\"]\n}"}, "typeVersion": 1.2}, {"id": "2380c4ea-d804-45b2-8341-417afa2ae21f", "name": "RSS Feed <PERSON>gger", "type": "n8n-nodes-base.rssFeedReadTrigger", "position": [3140, 320], "parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"id": "782e9c61-7d51-499b-89b2-888415c5116e", "name": "Return article details", "type": "n8n-nodes-base.set", "position": [4140, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ebe28fc7-f166-4428-b3f3-b319f2d080df", "name": "tag_ids", "type": "array", "value": "={{ $json.tag_ids }}"}, {"id": "bc296683-2a93-42b4-a9a7-90a2bc22f37b", "name": "title", "type": "string", "value": "={{ $('MOCK article').item.json.title }}"}, {"id": "32dc0950-3708-447e-a3b6-a5c5ae9bdcd0", "name": "content", "type": "string", "value": "={{ $('MOCK article').item.json.content }}"}]}}, "typeVersion": 3.4}, {"id": "6b5ce61f-8351-40ab-9e63-51c3e85ce53d", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2200, 840], "parameters": {"options": {"destinationFieldName": "missing_tag"}, "fieldToSplitOut": "missing_tags"}, "typeVersion": 1}, {"id": "2338e3e8-cba4-48c8-8c1a-50019af70932", "name": "Loop over articles", "type": "n8n-nodes-base.splitInBatches", "position": [1980, 320], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "39b89004-6032-4d22-8bcc-3dfd1d793ed0", "name": "SET initial record", "type": "n8n-nodes-base.set", "position": [2200, 440], "parameters": {"options": {}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "ec0b93cb-de9d-41be-9d4b-6846d3ee14a2", "name": "GET WP tags", "type": "n8n-nodes-base.httpRequest", "position": [2440, 440], "parameters": {"url": "https://www.example.com/wp-json/wp/v2/tags", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "XXXXXXX", "name": "Example"}}, "executeOnce": true, "typeVersion": 4.2, "alwaysOutputData": true}, {"id": "cbabadef-9f5f-4402-8bd7-255f5c237ff9", "name": "POST WP tags", "type": "n8n-nodes-base.httpRequest", "position": [2420, 840], "parameters": {"url": "https://www.example.com/wp-json/wp/v2/tags", "method": "POST", "options": {}, "sendQuery": true, "authentication": "predefinedCredentialType", "queryParameters": {"parameters": [{"name": "slug", "value": "={{ $json.missing_tag }}"}, {"name": "name", "value": "={{ $json.missing_tag.replaceAll(\"-\",\" \").toTitleCase() }}"}]}, "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "XXXXXXX", "name": "Example"}}, "executeOnce": false, "typeVersion": 4.2}, {"id": "6bf40d39-4b42-413f-9502-3ca494f75bcb", "name": "GET updated WP tags", "type": "n8n-nodes-base.httpRequest", "position": [2700, 840], "parameters": {"url": "https://www.example.com/wp-json/wp/v2/tags", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "wordpressApi"}, "credentials": {"wordpressApi": {"id": "XXXXXXX", "name": "Example"}}, "executeOnce": true, "typeVersion": 4.2}, {"id": "aea9a631-0cd8-4ed8-9fb1-981b8e11f3dd", "name": "Keep matches", "type": "n8n-nodes-base.filter", "position": [2200, 1040], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8ec4fdfc-73f3-4d7b-96e4-f42a18252599", "operator": {"type": "array", "operation": "contains", "rightType": "any"}, "leftValue": "={{ $('SET initial record').first().json.tags.map(item => item.toLowerCase().replaceAll(\" \",\"-\")) }}", "rightValue": "={{ $json.slug }}"}]}}, "typeVersion": 2.2}, {"id": "6d71d7a5-495d-4809-b66f-9f1cba0d11c6", "name": "Combine tag_ids", "type": "n8n-nodes-base.aggregate", "position": [2420, 1040], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tag_ids", "fieldToAggregate": "id"}]}}, "typeVersion": 1}, {"id": "dc3cac68-dee8-4821-963b-b0594d1a7e0e", "name": "Combine slugs", "type": "n8n-nodes-base.aggregate", "position": [2700, 440], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tags", "fieldToAggregate": "slug"}]}}, "typeVersion": 1}, {"id": "8e0f668c-e3ac-4d70-9ffb-5515e6221c62", "name": "If", "type": "n8n-nodes-base.if", "position": [2440, 640], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "8d77d072-cb47-4fbb-831a-0e6f3ecefc71", "operator": {"type": "array", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.missing_tags }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "7988188d-07e6-4a36-94f2-e21d7677802e", "name": "MOCK article", "type": "n8n-nodes-base.set", "position": [3740, 320], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4a69cf1b-341a-40bc-a36a-b76c05bdd819", "name": "title", "type": "string", "value": "={{ $('RSS Feed Trigger').item.json.title }}"}, {"id": "63097eb0-6165-4365-a5b5-e9f3de65d715", "name": "content", "type": "string", "value": "={{ $('RSS Feed Trigger').item.json.content }}"}, {"id": "ae4859ec-ad14-403e-b5b6-53703fefe3f3", "name": "categories", "type": "array", "value": "={{ $('RSS Feed Trigger').item.json.categories }}"}, {"id": "3f94d5ac-5196-4ad0-acea-79c07b0ee2c6", "name": "tags", "type": "array", "value": "={{ $json.output.tags }}"}]}}, "typeVersion": 3.4}, {"id": "4578cb14-dc86-4bc4-8d59-f0c088574164", "name": "Return missing tags", "type": "n8n-nodes-base.code", "position": [2200, 640], "parameters": {"jsCode": "const new_ary = $('SET initial record').first().json.tags.map(x => x.toLowerCase().replaceAll(\" \",\"-\")).filter(x => !$input.first().json.tags.includes(x))\n\nreturn {\"missing_tags\": new_ary};"}, "typeVersion": 2}, {"id": "91c8dde5-58ce-4bf6-ac3c-0062cbf0046e", "name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [4360, 320], "parameters": {"title": "=Demo tagging post: {{ $json.title }}", "additionalFields": {"tags": "={{ $json.tag_ids }}", "content": "=This is a post to demo automatic tagging a WordPress postvia n8n. The following content could be rewritten in full or part with commentary using AI.\n\n{{ $json.content }}"}}, "credentials": {"wordpressApi": {"id": "XXXXXXX", "name": "Example"}}, "typeVersion": 1}, {"id": "8257534e-f433-4225-a795-230fd367cc01", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [3000, 200], "parameters": {"color": 7, "width": 1673.0029952487134, "height": 1061.6563737812796, "content": "## Demo Usage in Another Workflow (Tagging an article discovered with an RSS feed)"}, "typeVersion": 1}, {"id": "b14e6fda-c569-4ada-90d9-77b61049c531", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1680, 198.96245932022566], "parameters": {"color": 7, "width": 1243.102096674096, "height": 1077.24135750937, "content": "## Auto-Tag Posts in WordPress\n\nThis workflow allows you to hand off the responsibility of tagging content for WordPress to an AI Agent in n8n with no data entry required."}, "typeVersion": 1}, {"id": "21420d0f-a5c9-4eac-b6d9-06d3a6de5fb9", "name": "Demo Usage in Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1780, 320], "parameters": {}, "typeVersion": 1}, {"id": "7571b196-3827-478f-b032-84d99adf4aa8", "name": "Auto-Tag Posts in WordPress", "type": "n8n-nodes-base.executeWorkflow", "position": [3940, 320], "parameters": {"mode": "each", "options": {}, "workflowId": {"__rl": true, "mode": "id", "value": "siXUnQhJpCJ9rHzu"}}, "typeVersion": 1.1}, {"id": "e5b63f63-09a6-452d-9d26-8501fc49d7fe", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2640, 140], "parameters": {"color": 5, "width": 256.**************, "height": 146.*************, "content": "## Copy this workflow\n\nYou can use it inline by removing the Called by Another Workflow trigger, or as-is as a subworkflow"}, "typeVersion": 1}, {"id": "2ea9fbdd-b492-4030-b640-227a163d70ef", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3040, 980], "parameters": {"width": 409.*************, "height": 248.*************, "content": "Handing off tagging and categorization fully to AI lets you **put your WordPress account on autopilot** without a human-in-the-loop.\n\nIn this example the application is use-case agnostic, but with this workflow you can:\n1. Use AI to rewrite content with original thoughts and tags\n2. Ensure healthy information architecture on your site\n3. Quickly generate multivariate tag and category combinations for optimal SEO"}, "typeVersion": 1}, {"id": "57cfa462-fc71-4173-b7c9-8253c4e240d1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3900, 500], "parameters": {"color": 3, "width": 369.**************, "height": 103.**************, "content": "### To ensure data can be passed to subsequent nodes, make sure to select \"Run Once for Each Item\" if executing a subworkflow"}, "typeVersion": 1}, {"id": "7f1dfade-07be-49b7-b5ee-99b58f1e6cc7", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2640, 660], "parameters": {"color": 6, "width": 211.*************, "content": "## What's this? \nIf there are missing tags we create them in WP, otherwise we keep get them all from WP and keep the relevant ones."}, "typeVersion": 1}, {"id": "61711c71-3e45-4b06-80a8-b651177b585d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1960, 540], "parameters": {"color": 3, "width": 174.33565557367925, "height": 251.80401948434695, "content": "## What's this? \nOne of the few potential failure points in this workflow, when checking for missing tags it's important that both the generated tags and the existing tags are in the same case (snake, dash, title)."}, "typeVersion": 1}, {"id": "31db85c9-e4c2-4409-9d92-7eb005223de0", "name": "Generate tags for article", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3360, 320], "parameters": {"text": "=Please provide 3-5 suitable tags for the following article:\n\n{{ $json.content }}\n\nTag Formatting Rules:\n1. Tags should be in title case", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "7d6eac92-6f6f-44a4-8dce-0830440a9dff", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1600, 1040], "parameters": {"width": 285.2555025627061, "content": "## ! A note about cases !\nIf you want your tags to follow a different case than I am using (dash case for slug, title case for name), then you will need to update a few nodes in this workflow."}, "typeVersion": 1}, {"id": "133be2f7-071b-4651-b3b5-8052a64b7f49", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2600, 1200], "parameters": {"color": 5, "width": 296.**************, "content": "## Ready for a challenge?\n\nMake this subworkflow executable for both categories and tags, accounting for different API calls to different endpoints."}, "typeVersion": 1}, {"id": "7807e967-ac3d-4a4d-bd9d-f123d57e1676", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [4400, 1155.*************], "parameters": {"color": 4, "width": 244.*************, "height": 87.**************, "content": "## About the maker\n**[<PERSON> on LinkedIn](https://www.linkedin.com/in/ludwiggerdes)**"}, "typeVersion": 1}], "active": false, "pinData": {"Generate tags for article": [{"json": {"output": {"tags": ["Team Achievements", "Global Community", "Product Growth", "2024 Highlights", "Reflecting on Progress"]}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "3acdf19c-288e-4a3b-87ae-5adbf44285fe", "connections": {"If": {"main": [[{"node": "GET updated WP tags", "type": "main", "index": 0}], [{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "POST WP tags", "type": "main", "index": 0}]]}, "GET WP tags": {"main": [[{"node": "Combine slugs", "type": "main", "index": 0}]]}, "Keep matches": {"main": [[{"node": "Combine tag_ids", "type": "main", "index": 0}]]}, "MOCK article": {"main": [[{"node": "Auto-Tag Posts in WordPress", "type": "main", "index": 0}]]}, "POST WP tags": {"main": [[{"node": "GET updated WP tags", "type": "main", "index": 0}]]}, "Combine slugs": {"main": [[{"node": "Return missing tags", "type": "main", "index": 0}]]}, "Combine tag_ids": {"main": [[{"node": "Loop over articles", "type": "main", "index": 0}]]}, "RSS Feed Trigger": {"main": [[{"node": "Generate tags for article", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Generate tags for article", "type": "ai_languageModel", "index": 0}]]}, "Loop over articles": {"main": [[], [{"node": "SET initial record", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "SET initial record": {"main": [[{"node": "GET WP tags", "type": "main", "index": 0}]]}, "GET updated WP tags": {"main": [[{"node": "Keep matches", "type": "main", "index": 0}]]}, "Return missing tags": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Return article details": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Generate tags for article", "type": "ai_outputParser", "index": 0}]]}, "Generate tags for article": {"main": [[{"node": "MOCK article", "type": "main", "index": 0}]]}, "Auto-Tag Posts in WordPress": {"main": [[{"node": "Return article details", "type": "main", "index": 0}]]}, "Demo Usage in Another Workflow": {"main": [[{"node": "Loop over articles", "type": "main", "index": 0}]]}}}